#!/usr/bin/env python3
"""
Debug the scaler issue with the fixed points model
"""

import torch
import numpy as np
from enhanced_player_props_pipeline import EnhancedPlayerPropsModel

def main():
    print("🔍 DEBUGGING SCALER ISSUE")
    print("="*40)
    
    try:
        # Load checkpoint
        checkpoint = torch.load('models/points_enhanced_model_FIXED_v2.pt', map_location='cpu')
        
        # Create model with correct architecture
        model = EnhancedPlayerPropsModel(
            input_dim=54,
            hidden_dims=[512, 256, 128, 64],
            dropout_rate=0.2,
            use_batch_norm=True
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Get scaler parameters
        scaler_params = checkpoint['feature_scaler_params']
        mean_array = np.array(scaler_params['mean_'])
        scale_array = np.array(scaler_params['scale_'])
        
        print(f"📊 Scaler stats:")
        print(f"   Mean range: [{mean_array.min():.3f}, {mean_array.max():.3f}]")
        print(f"   Scale range: [{scale_array.min():.3f}, {scale_array.max():.3f}]")
        print(f"   Scale mean: {scale_array.mean():.3f}")
        
        # Test with zero features (should give baseline prediction)
        zero_features = np.zeros(54)
        zero_scaled = (zero_features - mean_array) / scale_array
        
        with torch.no_grad():
            zero_tensor = torch.FloatTensor(zero_scaled).unsqueeze(0)
            baseline_pred = model(zero_tensor).squeeze().item()
        
        print(f"🎯 Baseline prediction (zero features): {baseline_pred:.1f}")
        
        # Test with mean features (should be close to target mean)
        mean_features = mean_array.copy()
        mean_scaled = (mean_features - mean_array) / scale_array  # Should be all zeros
        
        with torch.no_grad():
            mean_tensor = torch.FloatTensor(mean_scaled).unsqueeze(0)
            mean_pred = model(mean_tensor).squeeze().item()
        
        print(f"🎯 Mean features prediction: {mean_pred:.1f}")
        
        # Test with small variations around mean
        print(f"\n🔍 Testing small variations around mean:")
        for i, variation in enumerate([-0.5, -0.2, 0.0, 0.2, 0.5]):
            test_features = mean_array + variation
            test_scaled = (test_features - mean_array) / scale_array
            
            with torch.no_grad():
                test_tensor = torch.FloatTensor(test_scaled).unsqueeze(0)
                pred = model(test_tensor).squeeze().item()
            
            print(f"   Variation {variation:+.1f}: {pred:.1f} points")
        
        # Check if there's a scaling issue
        print(f"\n🔍 Checking for extreme scaling:")
        extreme_scales = scale_array[scale_array > 10]
        if len(extreme_scales) > 0:
            print(f"⚠️ Found {len(extreme_scales)} features with scale > 10")
            print(f"   Max scale: {scale_array.max():.3f}")
        else:
            print("✅ No extreme scaling detected")
            
        # The target should be around 13.5 (mean from training)
        expected_mean = 13.5
        print(f"\n📊 Expected target mean: {expected_mean}")
        print(f"📊 Model baseline: {baseline_pred:.1f}")
        
        if abs(baseline_pred - expected_mean) > 20:
            print("❌ MAJOR SCALING ISSUE - baseline too far from expected")
        elif abs(baseline_pred - expected_mean) > 10:
            print("⚠️ Moderate scaling issue")
        else:
            print("✅ Baseline prediction reasonable")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
