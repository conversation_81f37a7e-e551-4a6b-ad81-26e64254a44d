#!/usr/bin/env python3
"""
🔍 INSPECT FIXED MODELS
======================

Inspect the structure of the fixed enhanced models
"""

import torch
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def inspect_model(model_path: str):
    """Inspect model structure"""
    
    logger.info(f"\n🔍 Inspecting: {model_path}")
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        
        logger.info(f"📊 Checkpoint keys: {list(checkpoint.keys())}")
        
        for key, value in checkpoint.items():
            if isinstance(value, dict):
                logger.info(f"   {key}: dict with {len(value)} items")
                if len(value) < 10:  # Show small dicts
                    for k, v in value.items():
                        logger.info(f"      {k}: {type(v)}")
            elif isinstance(value, list):
                logger.info(f"   {key}: list with {len(value)} items")
            else:
                logger.info(f"   {key}: {type(value)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error inspecting {model_path}: {e}")
        return False

def main():
    """Inspect fixed models"""
    
    logger.info("🔍 INSPECTING FIXED ENHANCED MODELS")
    logger.info("="*40)
    
    models = [
        'models/points_enhanced_model_fixed.pt',
        'models/rebounds_enhanced_model_fixed.pt'
    ]
    
    for model_path in models:
        inspect_model(model_path)

if __name__ == "__main__":
    main()
