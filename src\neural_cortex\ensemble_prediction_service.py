#!/usr/bin/env python3
"""
🎯 ENSEMBLE PREDICTION SERVICE
=============================

Service for ensemble averaging of multiple neural models to reduce variance
and tighten error lines for player props predictions.
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import logging
from sklearn.preprocessing import StandardScaler

from .enhanced_player_props_pipeline import EnhancedPlayerPropsNetwork

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnsemblePredictionService:
    """Service for ensemble predictions using multiple trained models"""
    
    def __init__(self, model_base_path: str, ensemble_size: int = 5):
        self.model_base_path = model_base_path
        self.ensemble_size = ensemble_size
        self.models = []
        self.scalers = []
        self.feature_names = []
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load ensemble models
        self._load_ensemble_models()
    
    def _load_ensemble_models(self):
        """Load all ensemble models"""
        
        logger.info(f"🎲 Loading ensemble of {self.ensemble_size} models")
        
        for i in range(self.ensemble_size):
            model_path = f"{self.model_base_path}_ensemble_{i}.pt"
            
            if Path(model_path).exists():
                try:
                    checkpoint = torch.load(model_path, map_location=self.device)
                    
                    # Extract model configuration
                    config = checkpoint['config']
                    feature_names = checkpoint['feature_names']
                    
                    # Create model
                    model = EnhancedPlayerPropsNetwork(
                        input_dim=len(feature_names),
                        hidden_dim=config.get('hidden_dim', 256),
                        num_layers=config.get('num_layers', 4),
                        dropout_rate=config.get('dropout_rate', 0.4),
                        use_calibration=config.get('use_calibration_layer', True)
                    ).to(self.device)
                    
                    # Load model weights
                    model.load_state_dict(checkpoint['model_state_dict'])
                    model.eval()
                    
                    # Create scalers
                    feature_scaler = StandardScaler()
                    target_scaler = StandardScaler()
                    
                    # Load scaler parameters
                    if 'feature_scaler_params' in checkpoint:
                        feature_params = checkpoint['feature_scaler_params']
                        for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                            if attr in feature_params:
                                setattr(feature_scaler, attr, feature_params[attr])
                    
                    if 'target_scaler_params' in checkpoint:
                        target_params = checkpoint['target_scaler_params']
                        for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                            if attr in target_params:
                                setattr(target_scaler, attr, target_params[attr])
                    
                    # Store model and scalers
                    self.models.append(model)
                    self.scalers.append({
                        'feature_scaler': feature_scaler,
                        'target_scaler': target_scaler
                    })
                    
                    # Store feature names (should be same for all models)
                    if not self.feature_names:
                        self.feature_names = feature_names
                    
                    logger.info(f"✅ Loaded ensemble model {i+1}: {model_path}")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to load ensemble model {i}: {e}")
            else:
                logger.warning(f"⚠️ Ensemble model {i} not found: {model_path}")
        
        logger.info(f"🎯 Ensemble loaded: {len(self.models)}/{self.ensemble_size} models")
        
        if len(self.models) == 0:
            raise ValueError("No ensemble models loaded successfully")
    
    def predict_ensemble(self, player_data: Dict[str, Any]) -> Dict[str, float]:
        """Make ensemble prediction for a player"""
        
        try:
            # Create features
            features = self._create_enhanced_features(player_data)
            
            # Get predictions from all models
            predictions = []
            confidences = []
            
            for i, (model, scalers) in enumerate(zip(self.models, self.scalers)):
                try:
                    # Scale features
                    features_scaled = scalers['feature_scaler'].transform(features.reshape(1, -1))
                    features_tensor = torch.tensor(features_scaled, dtype=torch.float32).to(self.device)
                    
                    # Predict
                    with torch.no_grad():
                        raw_prediction = model(features_tensor).cpu().item()
                    
                    # Unscale prediction
                    prediction = scalers['target_scaler'].inverse_transform([[raw_prediction]])[0][0]
                    
                    # Apply realistic bounds
                    prediction = max(0, min(prediction, 50))  # Reasonable bounds for points
                    
                    predictions.append(prediction)
                    
                    # Calculate confidence (inverse of prediction variance)
                    confidence = 1.0 / (1.0 + abs(prediction - np.mean(predictions) if predictions else prediction))
                    confidences.append(confidence)
                    
                except Exception as e:
                    logger.warning(f"⚠️ Model {i} prediction failed: {e}")
                    continue
            
            if not predictions:
                raise ValueError("No successful predictions from ensemble")
            
            # Calculate ensemble statistics
            ensemble_prediction = np.mean(predictions)
            prediction_std = np.std(predictions)
            ensemble_confidence = np.mean(confidences)
            
            # Calculate prediction interval
            prediction_lower = ensemble_prediction - 1.96 * prediction_std  # 95% CI
            prediction_upper = ensemble_prediction + 1.96 * prediction_std
            
            return {
                'prediction': ensemble_prediction,
                'confidence': ensemble_confidence,
                'std': prediction_std,
                'lower_bound': max(0, prediction_lower),
                'upper_bound': min(50, prediction_upper),
                'individual_predictions': predictions,
                'models_used': len(predictions)
            }
            
        except Exception as e:
            logger.error(f"❌ Ensemble prediction failed: {e}")
            return {
                'prediction': 15.0,  # Default fallback
                'confidence': 0.1,
                'std': 5.0,
                'lower_bound': 10.0,
                'upper_bound': 20.0,
                'individual_predictions': [],
                'models_used': 0
            }
    
    def _create_enhanced_features(self, player_data: Dict[str, Any]) -> np.ndarray:
        """Create enhanced features matching training format"""
        
        # Extract basic stats
        points_pg = float(player_data.get('points', 15.0))
        rebounds_pg = float(player_data.get('rebounds', 5.0))
        assists_pg = float(player_data.get('assists', 3.0))
        steals_pg = float(player_data.get('steals', 1.0))
        blocks_pg = float(player_data.get('blocks', 0.5))
        threes_pg = float(player_data.get('threes', 1.0))
        
        # Minutes and usage
        minutes_per_game = float(player_data.get('minutes_per_game', 25.0))
        games_played = float(player_data.get('games_played', 30))
        total_minutes = minutes_per_game * games_played  # Season total for consistency
        
        usage_rate = float(player_data.get('usage_rate', 20.0))
        
        # Shooting stats
        fga_per_game = float(player_data.get('field_goal_attempts', 10.0))
        fg_pct = float(player_data.get('field_goal_percentage', 0.45))
        ft_pct = float(player_data.get('free_throw_percentage', 0.75))
        
        # Enhanced features
        team_pace = float(player_data.get('team_pace', 100.0))
        opponent_def_rating = float(player_data.get('opponent_def_rating', 110.0))
        
        # Recent form
        recent_points_avg_5 = float(player_data.get('recent_points_avg_5', points_pg))
        recent_points_avg_10 = float(player_data.get('recent_points_avg_10', points_pg))
        
        # Create feature dictionary
        feature_dict = {
            'points': points_pg,
            'rebounds': rebounds_pg,
            'assists': assists_pg,
            'steals': steals_pg,
            'blocks': blocks_pg,
            'threes': threes_pg,
            'games_played': games_played,
            'minutes_per_game': total_minutes,  # Season total
            'field_goal_percentage': fg_pct,
            'free_throw_percentage': ft_pct,
            'usage_rate': usage_rate,
            'field_goal_attempts': fga_per_game,
            'team_pace': team_pace,
            'opponent_def_rating': opponent_def_rating,
            'recent_points_avg_5': recent_points_avg_5,
            'recent_points_avg_10': recent_points_avg_10,
            
            # Interaction features
            'minutes_x_usage': total_minutes * usage_rate,
            'efficiency_x_volume': (points_pg / max(minutes_per_game, 1)) * minutes_per_game,
            'form_per_minute': recent_points_avg_5 / max(minutes_per_game, 1),
            'usage_x_pace': usage_rate * team_pace,
            'shots_x_accuracy': fga_per_game * fg_pct,
            'matchup_difficulty': opponent_def_rating * usage_rate,
            
            # Additional derived features
            'points_per_minute': points_pg / max(minutes_per_game, 1),
            'rebounds_per_minute': rebounds_pg / max(minutes_per_game, 1),
            'assists_per_minute': assists_pg / max(minutes_per_game, 1),
            'total_stats': points_pg + rebounds_pg + assists_pg,
            'offensive_stats': points_pg + assists_pg,
            'defensive_stats': rebounds_pg + steals_pg + blocks_pg
        }
        
        # Create feature vector in correct order
        features = []
        for feature_name in self.feature_names:
            if feature_name in feature_dict:
                features.append(feature_dict[feature_name])
            else:
                # Default value for missing features
                features.append(0.5)
        
        return np.array(features, dtype=np.float32)
    
    def get_ensemble_info(self) -> Dict[str, Any]:
        """Get information about the loaded ensemble"""
        
        return {
            'ensemble_size': len(self.models),
            'expected_size': self.ensemble_size,
            'feature_count': len(self.feature_names),
            'feature_names': self.feature_names[:10],  # First 10 features
            'device': str(self.device),
            'models_loaded': len(self.models) > 0
        }

def create_ensemble_service(model_base_path: str, ensemble_size: int = 5) -> EnsemblePredictionService:
    """Factory function to create ensemble service"""
    
    try:
        service = EnsemblePredictionService(model_base_path, ensemble_size)
        logger.info(f"✅ Ensemble service created successfully")
        return service
    except Exception as e:
        logger.error(f"❌ Failed to create ensemble service: {e}")
        raise

# Example usage
if __name__ == "__main__":
    # Test ensemble service
    try:
        service = create_ensemble_service(
            model_base_path="./models/enhanced_basketball_models/enhanced_points_model_v2",
            ensemble_size=5
        )
        
        # Test prediction
        test_player = {
            'points': 18.0,
            'rebounds': 10.0,
            'assists': 2.0,
            'steals': 1.0,
            'blocks': 2.0,
            'threes': 0.0,
            'minutes_per_game': 34.0,
            'games_played': 30,
            'usage_rate': 28.0,
            'field_goal_attempts': 16.0,
            'field_goal_percentage': 0.563
        }
        
        result = service.predict_ensemble(test_player)
        
        print(f"🎯 Ensemble Prediction Results:")
        print(f"   Prediction: {result['prediction']:.1f} points")
        print(f"   Confidence: {result['confidence']:.3f}")
        print(f"   Std Dev: {result['std']:.2f}")
        print(f"   95% CI: [{result['lower_bound']:.1f}, {result['upper_bound']:.1f}]")
        print(f"   Models used: {result['models_used']}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
