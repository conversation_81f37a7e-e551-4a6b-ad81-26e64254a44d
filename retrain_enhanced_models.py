#!/usr/bin/env python3
"""
🔧 RETRAIN ENHANCED MODELS - FIXED SCALING
==========================================

Retrain enhanced models with proper StandardScaler to fix extreme predictions
"""

import sys
import os
sys.path.append('.')

from enhanced_player_props_pipeline import EnhancedTrainingPipeline, create_validation_data_for_training
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Retrain enhanced models with fixed scaling"""
    
    logger.info("🔧 RETRAINING ENHANCED MODELS - SCALING FIX")
    logger.info("="*60)
    
    # Create validation data
    validation_df = create_validation_data_for_training()
    logger.info(f"📊 Using {len(validation_df)} validation examples for training")
    
    # Train enhanced models for points and rebounds
    for prop_type in ['points', 'rebounds']:
        logger.info(f"\n🚀 Retraining enhanced {prop_type} model with fixed scaling...")
        
        try:
            # Create pipeline
            pipeline = EnhancedTrainingPipeline(prop_type)
            
            # Create enhanced training data
            X, y, feature_names = pipeline.create_enhanced_training_data(validation_df)
            
            logger.info(f"📊 Training data shape: {X.shape}")
            logger.info(f"📊 Target ({prop_type}) range: [{y.min():.1f}, {y.max():.1f}], mean: {y.mean():.2f}")
            
            # Train model with fixed scaling
            results = pipeline.train_enhanced_model(X, y, feature_names)
            
            # Save model
            save_path = f"models/{prop_type}_enhanced_model_fixed.pt"
            pipeline.save_enhanced_model(results, save_path)
            
            logger.info(f"✅ Enhanced {prop_type} model retrained successfully!")
            logger.info(f"📊 Performance: MAE={results['final_mae']:.3f}, R²={results['r2_score']:.3f}")
            logger.info(f"💾 Saved to: {save_path}")
            
        except Exception as e:
            logger.error(f"❌ Error retraining {prop_type} model: {e}")
            import traceback
            traceback.print_exc()
    
    logger.info(f"\n🎉 RETRAINING COMPLETED!")
    logger.info("🔍 Next step: Test fixed models for realistic predictions")

if __name__ == "__main__":
    main()
