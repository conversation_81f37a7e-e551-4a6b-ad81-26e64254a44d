{"github.copilot.nextEditSuggestions.enabled": true, "python.analysis.nodeExecutable": "auto", "python.analysis.exclude": ["**/env", "**/.venv", "**/venv", "**/node_modules", "**/__pycache__", "**/build", "**/dist"], "python.analysis.memory.keepLibraryAst": true, "git.ignoreLimitWarning": true, "python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true}