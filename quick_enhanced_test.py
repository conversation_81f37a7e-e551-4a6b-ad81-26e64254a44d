#!/usr/bin/env python3
"""
🎯 QUICK ENHANCED MODEL TEST
===========================

Quick test of the enhanced models with 54-feature engineering
against the 7/5/25 validation data.
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from pathlib import Path
import logging
from typing import Dict, List
from sklearn.preprocessing import RobustScaler
import sys

# Add compatible model architecture
sys.path.append('.')
from compatible_model_architecture import predict_with_compatible_model

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedPlayerPropsModel(nn.Module):
    """Enhanced neural network for player props"""
    
    def __init__(self, input_dim: int = 54, hidden_dims: List[int] = [256, 256, 128], 
                 dropout_rate: float = 0.3, use_batch_norm: bool = True):
        super().__init__()
        
        self.input_dim = input_dim
        self.use_batch_norm = use_batch_norm
        
        # Build main network layers
        layers = []
        prev_dim = input_dim
        
        for i, hidden_dim in enumerate(hidden_dims):
            layers.append(nn.Linear(prev_dim, hidden_dim))
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            if dropout_rate > 0:
                layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim
        
        self.main_network = nn.Sequential(*layers)
        self.calibration = nn.Linear(prev_dim, 1)
    
    def forward(self, x):
        x = self.main_network(x)
        x = self.calibration(x)
        return x

def create_enhanced_features(player_data: Dict) -> pd.DataFrame:
    """Create enhanced 54-feature set for a player"""
    
    features = {}
    
    # 1. BASE STATS (6 features)
    base_stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    for stat in base_stats:
        features[stat] = player_data.get(stat, 0.0)
    
    # 2. GAME CONTEXT (4 features)
    features['games_played'] = 20.0
    features['minutes_per_game'] = 25.0
    features['field_goal_percentage'] = 0.45
    features['free_throw_percentage'] = 0.75
    
    # 3. PLAYER PROFILE (5 features)
    features['age'] = 26.0
    features['scoring_tier'] = 2.0
    features['rebounding_tier'] = 2.0
    features['playmaking_tier'] = 2.0
    features['defensive_tier'] = 2.0
    
    # 4. HIGH PERFORMER FLAGS (6 features)
    features['high_scorer'] = 1.0 if features['points'] > 15 else 0.0
    features['high_rebounder'] = 1.0 if features['rebounds'] > 6 else 0.0
    features['high_assists'] = 1.0 if features['assists'] > 4 else 0.0
    features['high_steals'] = 1.0 if features['steals'] > 1 else 0.0
    features['high_blocks'] = 1.0 if features['blocks'] > 0.5 else 0.0
    features['high_threes'] = 1.0 if features['threes'] > 2 else 0.0
    
    # 5. PER-MINUTE RATES (6 features)
    minutes = 25.0
    features['points_per_minute'] = features['points'] / minutes
    features['rebounds_per_minute'] = features['rebounds'] / minutes
    features['assists_per_minute'] = features['assists'] / minutes
    features['steals_per_minute'] = features['steals'] / minutes
    features['blocks_per_minute'] = features['blocks'] / minutes
    features['threes_per_minute'] = features['threes'] / minutes
    
    # 6. COMPOSITE STATS (3 features)
    features['total_stats'] = features['points'] + features['rebounds'] + features['assists']
    features['defensive_stats'] = features['steals'] + features['blocks']
    features['offensive_stats'] = features['points'] + features['assists'] + features['threes']
    
    # 7. ENHANCED FEATURES (12 features)
    features['usage_rate'] = 0.20
    features['pace_factor'] = 1.0
    features['offensive_rating'] = 100.0
    features['defensive_rating'] = 100.0
    
    # Rolling averages (simulated)
    np.random.seed(hash(player_data['player']) % 2**32)
    features['points_l5'] = features['points'] * np.random.uniform(0.8, 1.2)
    features['rebounds_l5'] = features['rebounds'] * np.random.uniform(0.8, 1.2)
    features['assists_l5'] = features['assists'] * np.random.uniform(0.8, 1.2)
    features['minutes_l5'] = 25.0 * np.random.uniform(0.9, 1.1)
    
    # Hot/Cold streak indicators
    features['hot_streak'] = 1.0 if features['points'] > 20 else 0.0
    features['cold_streak'] = 1.0 if features['points'] < 5 else 0.0
    features['form_trend'] = 0.5
    features['consistency_score'] = 0.75
    
    # 8. MATCHUP CONTEXT (8 features)
    features['opponent_def_rating'] = 105.0
    features['home_advantage'] = 0.0 if player_data.get('team') in ['LAS', 'GS'] else 1.0
    features['rest_days'] = 1.0
    features['back_to_back'] = 0.0
    features['season_progress'] = 0.5
    features['playoff_intensity'] = 0.0
    features['rivalry_game'] = 0.0
    features['national_tv'] = 0.0
    
    # 9. POSITION AND ROLE (4 features)
    features['position_encoded'] = 2.0
    features['starter_flag'] = 1.0
    features['bench_role'] = 0.0
    features['veteran_flag'] = 0.0
    
    return pd.DataFrame([features])

def load_enhanced_model(model_path: str):
    """Load enhanced model"""
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # Create model
        model = EnhancedPlayerPropsModel(input_dim=54)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Load scaler
        scaler = RobustScaler()
        scaler_params = checkpoint.get('feature_scaler_params', {})
        if 'center_' in scaler_params:
            scaler.center_ = np.array(scaler_params['center_'])
            scaler.scale_ = np.array(scaler_params['scale_'])
            scaler.n_features_in_ = 54
        
        return model, scaler
        
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        return None, None

def predict_enhanced(model, scaler, features_df: pd.DataFrame) -> float:
    """Make prediction with enhanced model"""
    
    try:
        # Scale features
        features_scaled = scaler.transform(features_df.values)
        
        # Convert to tensor
        features_tensor = torch.FloatTensor(features_scaled)
        
        # Make prediction
        with torch.no_grad():
            prediction = model(features_tensor).item()
        
        return max(0.0, prediction)
        
    except Exception as e:
        logger.error(f"Error making prediction: {e}")
        return 0.0

def main():
    """Main test function"""
    
    logger.info("🎯 QUICK ENHANCED MODEL TEST")
    logger.info("="*50)
    
    # Test data (key players from 7/5/25 games)
    test_players = [
        {"player": "Kelsey Mitchell", "team": "IND", "points": 28, "rebounds": 2},
        {"player": "Dearica Hamby", "team": "LAS", "points": 21, "rebounds": 11},
        {"player": "Napheesa Collier", "team": "MIN", "points": 21, "rebounds": 6}
    ]
    
    logger.info("\n📊 TESTING ENHANCED MODELS:")
    logger.info(f"{'Player':<20} {'Stat':<8} {'Actual':<8} {'Enhanced':<10} {'Error':<8}")
    logger.info("-" * 60)
    
    for player_data in test_players:
        # Test both points and rebounds
        for stat in ['points', 'rebounds']:
            actual = player_data[stat]
            
            # Enhanced model prediction
            enhanced_features = create_enhanced_features(player_data)
            enhanced_model_path = f'models/{stat}_enhanced_model.pt'
            
            if Path(enhanced_model_path).exists():
                model, scaler = load_enhanced_model(enhanced_model_path)
                if model is not None:
                    enhanced_pred = predict_enhanced(model, scaler, enhanced_features)
                    error = abs(enhanced_pred - actual)
                    logger.info(f"{player_data['player']:<20} {stat:<8} {actual:<8.1f} {enhanced_pred:<10.1f} {error:<8.1f}")
                else:
                    logger.error(f"Failed to load enhanced model: {enhanced_model_path}")
            else:
                logger.warning(f"Enhanced model not found: {enhanced_model_path}")
    
    logger.info("\n✅ Enhanced model test completed!")

if __name__ == "__main__":
    main()
