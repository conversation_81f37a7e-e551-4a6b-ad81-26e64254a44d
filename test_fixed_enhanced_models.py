#!/usr/bin/env python3
"""
🔍 TEST FIXED ENHANCED MODELS
============================

Test the retrained enhanced models to verify they produce realistic predictions
"""

import sys
import os
sys.path.append('.')

import torch
import pandas as pd
import numpy as np
from enhanced_player_props_pipeline import EnhancedFeatureEngineering, create_validation_data_for_training
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_fixed_model(prop_type: str, model_path: str):
    """Test a fixed enhanced model"""
    
    logger.info(f"\n🔍 Testing fixed {prop_type} model: {model_path}")
    
    try:
        # Load model
        checkpoint = torch.load(model_path, map_location='cpu')
        model = checkpoint['model']
        model.eval()
        
        # Get scaler parameters
        scaler_params = checkpoint['feature_scaler_params']
        feature_list = checkpoint['feature_list']
        
        logger.info(f"📊 Model loaded - Features: {len(feature_list)}")
        logger.info(f"📊 Scaler mean range: [{min(scaler_params['mean_']):.3f}, {max(scaler_params['mean_']):.3f}]")
        logger.info(f"📊 Scaler scale range: [{min(scaler_params['scale_']):.3f}, {max(scaler_params['scale_']):.3f}]")
        
        # Create test data
        validation_df = create_validation_data_for_training()
        feature_engineer = EnhancedFeatureEngineering(prop_type)
        
        # Test on a few examples
        test_players = [
            {"name": "Kelsey Mitchell", "points": 28, "rebounds": 3, "assists": 5},
            {"name": "Dearica Hamby", "points": 21, "rebounds": 11, "assists": 4},
            {"name": "Napheesa Collier", "points": 21, "rebounds": 6, "assists": 3}
        ]
        
        predictions = []
        
        for player in test_players:
            # Create features for this player
            player_data = validation_df.iloc[0].to_dict()  # Use first row as template
            player_data.update(player)
            
            # Generate features
            features_df = feature_engineer.create_comprehensive_features(player_data)
            
            # Ensure feature order matches training
            features_df = features_df.reindex(columns=feature_list, fill_value=0.0)
            features_array = features_df.values.astype(np.float32)
            
            # Apply scaling
            mean_array = np.array(scaler_params['mean_'])
            scale_array = np.array(scaler_params['scale_'])
            features_scaled = (features_array - mean_array) / scale_array
            
            # Make prediction
            with torch.no_grad():
                features_tensor = torch.FloatTensor(features_scaled)
                prediction = model(features_tensor).item()
            
            predictions.append({
                'player': player['name'],
                'actual': player[prop_type],
                'predicted': prediction,
                'error': abs(prediction - player[prop_type])
            })
            
            logger.info(f"👤 {player['name']}: Actual {player[prop_type]} {prop_type}, Predicted {prediction:.1f} (Error: {abs(prediction - player[prop_type]):.1f})")
        
        # Calculate summary stats
        errors = [p['error'] for p in predictions]
        mae = np.mean(errors)
        max_error = max(errors)
        
        # Check if predictions are realistic
        pred_values = [p['predicted'] for p in predictions]
        realistic_range = {
            'points': (5, 35),
            'rebounds': (1, 15),
            'assists': (1, 12),
            'steals': (0, 5),
            'blocks': (0, 4),
            'threes': (0, 8)
        }
        
        min_pred, max_pred = min(pred_values), max(pred_values)
        expected_min, expected_max = realistic_range.get(prop_type, (0, 50))
        
        is_realistic = expected_min <= min_pred and max_pred <= expected_max
        
        logger.info(f"📊 {prop_type.upper()} MODEL SUMMARY:")
        logger.info(f"   MAE: {mae:.2f}")
        logger.info(f"   Max Error: {max_error:.2f}")
        logger.info(f"   Prediction Range: [{min_pred:.1f}, {max_pred:.1f}]")
        logger.info(f"   Expected Range: [{expected_min}, {expected_max}]")
        logger.info(f"   Realistic: {'✅ YES' if is_realistic else '❌ NO'}")
        
        return {
            'prop_type': prop_type,
            'mae': mae,
            'max_error': max_error,
            'prediction_range': (min_pred, max_pred),
            'expected_range': (expected_min, expected_max),
            'is_realistic': is_realistic,
            'predictions': predictions
        }
        
    except Exception as e:
        logger.error(f"❌ Error testing {prop_type} model: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Test all fixed enhanced models"""
    
    logger.info("🔍 TESTING FIXED ENHANCED MODELS")
    logger.info("="*50)
    
    # Test fixed models
    models_to_test = [
        ('points', 'models/points_enhanced_model_fixed.pt'),
        ('rebounds', 'models/rebounds_enhanced_model_fixed.pt')
    ]
    
    results = []
    
    for prop_type, model_path in models_to_test:
        if os.path.exists(model_path):
            result = test_fixed_model(prop_type, model_path)
            if result:
                results.append(result)
        else:
            logger.warning(f"⚠️ Model not found: {model_path}")
    
    # Summary
    logger.info(f"\n🎯 FIXED MODELS SUMMARY:")
    logger.info("="*50)
    
    realistic_count = sum(1 for r in results if r['is_realistic'])
    total_count = len(results)
    
    for result in results:
        status = "✅ REALISTIC" if result['is_realistic'] else "❌ UNREALISTIC"
        logger.info(f"{result['prop_type'].upper()}: MAE={result['mae']:.2f}, Range={result['prediction_range']}, {status}")
    
    logger.info(f"\n📊 Overall: {realistic_count}/{total_count} models producing realistic predictions")
    
    if realistic_count == total_count:
        logger.info("🎉 SUCCESS: All fixed models are producing realistic predictions!")
        logger.info("🔍 Next step: Validate against 7/5/25 games")
    else:
        logger.info("⚠️ Some models still need adjustment")

if __name__ == "__main__":
    main()
