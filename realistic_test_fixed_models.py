#!/usr/bin/env python3
"""
🔍 REALISTIC TEST FIXED MODELS
=============================

Test fixed models with realistic player features
"""

import sys
import os
sys.path.append('.')

import torch
import numpy as np
from enhanced_player_props_pipeline import EnhancedPlayerPropsModel, EnhancedFeatureEngineer, create_validation_data_for_training
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_with_realistic_features(model_path: str, prop_type: str):
    """Test model with realistic player features"""
    
    logger.info(f"\n🔍 Testing {prop_type} model with realistic features")
    
    try:
        # Load model
        checkpoint = torch.load(model_path, map_location='cpu')
        model_config = checkpoint['model_config']
        model = EnhancedPlayerPropsModel(**model_config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Get scaler and feature info
        scaler_params = checkpoint['feature_scaler_params']
        feature_list = checkpoint['feature_list']
        
        # Create realistic test players
        test_players = [
            {
                "name": "Star Player",
                "points_per_game": 22.0,
                "rebounds_per_game": 8.0,
                "assists_per_game": 5.0,
                "minutes_per_game": 32.0,
                "age": 26.0,
                "games_played": 25.0
            },
            {
                "name": "Role Player", 
                "points_per_game": 12.0,
                "rebounds_per_game": 4.0,
                "assists_per_game": 2.0,
                "minutes_per_game": 22.0,
                "age": 24.0,
                "games_played": 28.0
            },
            {
                "name": "Bench Player",
                "points_per_game": 6.0,
                "rebounds_per_game": 2.0,
                "assists_per_game": 1.0,
                "minutes_per_game": 15.0,
                "age": 22.0,
                "games_played": 20.0
            }
        ]
        
        # Create feature engineer
        feature_engineer = EnhancedFeatureEngineer(prop_type)
        
        predictions = []
        
        for player in test_players:
            # Generate comprehensive features
            features_df = feature_engineer.create_comprehensive_features(player)
            
            # Ensure feature order matches training
            features_df = features_df.reindex(columns=feature_list, fill_value=0.0)
            features_array = features_df.values.astype(np.float32).flatten()
            
            # Apply scaling
            mean_array = np.array(scaler_params['mean_'])
            scale_array = np.array(scaler_params['scale_'])
            features_scaled = (features_array - mean_array) / scale_array
            
            # Make prediction
            with torch.no_grad():
                features_tensor = torch.FloatTensor(features_scaled).unsqueeze(0)
                prediction = model(features_tensor).item()
            
            predictions.append({
                'player': player['name'],
                'prediction': prediction,
                'expected_baseline': player.get(f'{prop_type}_per_game', 10.0)
            })
            
            logger.info(f"👤 {player['name']}: {prediction:.1f} {prop_type} (baseline: {player.get(f'{prop_type}_per_game', 10.0):.1f})")
        
        # Check if predictions are realistic
        pred_values = [p['prediction'] for p in predictions]
        min_pred, max_pred = min(pred_values), max(pred_values)
        
        realistic_ranges = {
            'points': (3, 40),
            'rebounds': (0, 18),
            'assists': (0, 15),
            'steals': (0, 6),
            'blocks': (0, 5),
            'threes': (0, 10)
        }
        
        expected_min, expected_max = realistic_ranges.get(prop_type, (0, 50))
        is_realistic = expected_min <= min_pred and max_pred <= expected_max
        
        # Check if predictions make sense relative to player types
        star_pred = predictions[0]['prediction']
        role_pred = predictions[1]['prediction'] 
        bench_pred = predictions[2]['prediction']
        
        logical_order = star_pred >= role_pred >= bench_pred
        
        logger.info(f"📊 {prop_type.upper()} RESULTS:")
        logger.info(f"   Range: [{min_pred:.1f}, {max_pred:.1f}]")
        logger.info(f"   Expected: [{expected_min}, {expected_max}]")
        logger.info(f"   Realistic Range: {'✅' if is_realistic else '❌'}")
        logger.info(f"   Logical Order: {'✅' if logical_order else '❌'}")
        
        return {
            'prop_type': prop_type,
            'predictions': predictions,
            'range': (min_pred, max_pred),
            'expected_range': (expected_min, expected_max),
            'is_realistic': is_realistic,
            'logical_order': logical_order,
            'overall_good': is_realistic and logical_order
        }
        
    except Exception as e:
        logger.error(f"❌ Error testing {prop_type}: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Test fixed models with realistic features"""
    
    logger.info("🔍 REALISTIC TEST OF FIXED ENHANCED MODELS")
    logger.info("="*50)
    
    models = [
        ('points', 'models/points_enhanced_model_fixed.pt'),
        ('rebounds', 'models/rebounds_enhanced_model_fixed.pt')
    ]
    
    results = []
    
    for prop_type, model_path in models:
        if os.path.exists(model_path):
            result = test_with_realistic_features(model_path, prop_type)
            if result:
                results.append(result)
        else:
            logger.warning(f"⚠️ Model not found: {model_path}")
    
    # Summary
    logger.info(f"\n🎯 OVERALL SUMMARY:")
    logger.info("="*50)
    
    good_models = sum(1 for r in results if r['overall_good'])
    total_models = len(results)
    
    for result in results:
        status = "✅ GOOD" if result['overall_good'] else "❌ NEEDS WORK"
        logger.info(f"{result['prop_type'].upper()}: {status}")
        logger.info(f"   Range: {result['range']}")
        logger.info(f"   Realistic: {'✅' if result['is_realistic'] else '❌'}")
        logger.info(f"   Logical: {'✅' if result['logical_order'] else '❌'}")
    
    logger.info(f"\n📊 {good_models}/{total_models} models working well")
    
    if good_models == total_models:
        logger.info("🎉 SUCCESS: All fixed models are working well!")
        logger.info("🔍 Ready for validation against 7/5/25 games")
    else:
        logger.info("⚠️ Some models need further adjustment")

if __name__ == "__main__":
    main()
