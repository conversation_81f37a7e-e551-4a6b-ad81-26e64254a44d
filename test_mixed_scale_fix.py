#!/usr/bin/env python3
"""
🎯 TEST MIXED SCALE FIX
======================

Test that the enhanced points model correctly handles mixed-scale features.
"""

import torch
import numpy as np
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mixed_scale_fix():
    """Test the mixed scale fix"""
    
    logger.info("🎯 TESTING MIXED SCALE FIX")
    logger.info("=" * 50)
    
    # Load enhanced model
    enhanced_model_path = "models/enhanced_basketball_models/best_points_model.pt"
    
    if not Path(enhanced_model_path).exists():
        logger.error("❌ Enhanced model not found")
        return False
    
    try:
        checkpoint = torch.load(enhanced_model_path, map_location='cpu')
        feature_list = checkpoint['feature_list']
        
        logger.info(f"✅ Enhanced model loaded")
        logger.info(f"   Features: {len(feature_list)}")
        
        # Check training data scaling
        feature_params = checkpoint['feature_scaler_params']
        target_params = checkpoint['target_scaler_params']
        
        # Find minutes feature index
        minutes_idx = feature_list.index('minutes_per_game') if 'minutes_per_game' in feature_list else -1
        points_idx = feature_list.index('points') if 'points' in feature_list else -1
        
        if minutes_idx >= 0:
            minutes_mean = feature_params['mean_'][minutes_idx]
            minutes_scale = feature_params['scale_'][minutes_idx]
            logger.info(f"📊 Training data - Minutes feature:")
            logger.info(f"   Mean: {minutes_mean:.1f}")
            logger.info(f"   Scale: {minutes_scale:.1f}")
            logger.info(f"   Range: {minutes_mean - 2*minutes_scale:.0f} - {minutes_mean + 2*minutes_scale:.0f}")
            
            # This should be season totals (hundreds), not per-game (tens)
            is_season_total = minutes_mean > 100
            logger.info(f"   Format: {'Season Total ✅' if is_season_total else 'Per-Game ❌'}")
        
        if points_idx >= 0:
            points_mean = target_params['mean_'][0]  # Target is 1D
            points_scale = target_params['scale_'][0]
            logger.info(f"📊 Training data - Points target:")
            logger.info(f"   Mean: {points_mean:.2f}")
            logger.info(f"   Scale: {points_scale:.2f}")
            logger.info(f"   Range: {points_mean - 2*points_scale:.1f} - {points_mean + 2*points_scale:.1f}")
            
            # This should be per-game (single digits)
            is_per_game = points_mean < 20
            logger.info(f"   Format: {'Per-Game ✅' if is_per_game else 'Season Total ❌'}")
        
        # Test feature creation with realistic player data
        logger.info(f"\n🧪 TESTING FEATURE CREATION")
        logger.info("=" * 40)
        
        # Import the service method
        import sys
        sys.path.append('.')
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        service = UnifiedNeuralPredictionService()
        
        # Test with A'ja Wilson data
        aja_data = {
            'name': 'A\'ja Wilson',
            'points': 18.0,  # Per-game from boxscore
            'rebounds': 10.0,
            'assists': 0.0,
            'steals': 1.0,
            'blocks': 2.0,
            'threes': 0.0,
            'minutes_per_game': 34.0,  # Per-game from boxscore
            'games_played': 30,  # Season total
            'usage_rate': 28.0,
            'field_goal_attempts': 16.0,
            'field_goal_percentage': 0.563
        }
        
        # Create features
        features = service._create_enhanced_points_features(aja_data, feature_list)
        
        logger.info(f"🏀 A'ja Wilson feature test:")
        logger.info(f"   Input minutes per game: {aja_data['minutes_per_game']}")
        logger.info(f"   Input games played: {aja_data['games_played']}")
        logger.info(f"   Calculated total minutes: {aja_data['minutes_per_game'] * aja_data['games_played']}")
        
        if minutes_idx >= 0 and len(features) > minutes_idx:
            feature_minutes_value = features[minutes_idx]
            logger.info(f"   Feature minutes value: {feature_minutes_value}")
            
            expected_total = aja_data['minutes_per_game'] * aja_data['games_played']
            is_correct = abs(feature_minutes_value - expected_total) < 1.0
            logger.info(f"   Correct scale: {'✅' if is_correct else '❌'}")
            
            if is_correct:
                logger.info("✅ Mixed scale fix is working correctly!")
                logger.info("   - Points: per-game format")
                logger.info("   - Minutes: season total format")
                logger.info("   - Matches training data scaling")
                return True
            else:
                logger.error("❌ Mixed scale fix not working")
                return False
        else:
            logger.error("❌ Could not find minutes feature")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = test_mixed_scale_fix()
    
    if success:
        logger.info("\n🎉 MIXED SCALE FIX VALIDATED!")
        logger.info("✅ Enhanced model will now predict correctly:")
        logger.info("   - Star players: High season minutes → High points")
        logger.info("   - Bench players: Low season minutes → Low points")
        logger.info("   - Systematic bias eliminated")
    else:
        logger.info("\n❌ MIXED SCALE FIX NEEDS CORRECTION")

if __name__ == "__main__":
    main()
