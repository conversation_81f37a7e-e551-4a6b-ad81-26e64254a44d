#!/usr/bin/env python3
"""
Debug script to check neural model predictions
"""

import torch
import numpy as np
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DEBUG")

def debug_model():
    """Debug the neural model predictions"""
    
    # Load the model checkpoint
    model_path = "best_wnba_neural_model_fixed.pth"
    checkpoint = torch.load(model_path, map_location='cpu')
    
    print("🔍 Model checkpoint contents:")
    for key in checkpoint.keys():
        print(f"  - {key}: {type(checkpoint[key])}")
    
    print(f"\n📊 Model info:")
    print(f"  - Input dim: {checkpoint['input_dim']}")
    print(f"  - Feature names: {checkpoint['feature_names']}")
    print(f"  - Scaler params keys: {list(checkpoint['scaler_params'].keys())}")
    
    # Test with different team stats
    teams_data = {
        "Las Vegas Aces": [85.2, 31.5, 75.8, 0.416, 8.2, 24.1, 0.340, 14.0, 18.5, 0.757, 9.8, 25.2, 35.0, 21.5, 12.8, 7.2, 4.1],
        "Los Angeles Sparks": [70.8, 26.4, 67.7, 0.390, 5.0, 18.8, 0.266, 13.0, 20.2, 0.644, 7.3, 21.4, 28.7, 16.0, 11.0, 4.6, 1.4],
        "New York Liberty": [83.1, 30.8, 74.2, 0.415, 7.9, 23.5, 0.336, 13.6, 17.8, 0.764, 9.5, 24.8, 34.3, 20.9, 12.5, 7.0, 3.8]
    }
    
    # Load model architecture
    from wnba_validation_test_runner import ProvenNeuralNetwork
    model = ProvenNeuralNetwork(input_dim=17, hidden_dim=64, dropout_rate=0.3)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"\n🧠 Testing predictions for different teams:")
    
    for team_name, stats in teams_data.items():
        # Apply scaling
        features = np.array(stats, dtype=np.float32)
        scaler_params = checkpoint['scaler_params']
        scaled_features = (features - scaler_params['mean_']) / scaler_params['scale_']
        
        # Make prediction
        with torch.no_grad():
            features_tensor = torch.FloatTensor(scaled_features).unsqueeze(0)
            outputs = model(features_tensor)
            
            if outputs.shape[-1] == 1:
                prob = torch.sigmoid(outputs[0]).item()
            else:
                probs = torch.softmax(outputs, dim=1)
                prob = probs[0][1].item()  # Win probability
        
        print(f"  {team_name}: {prob:.3f}")
        print(f"    Raw features: {features[:3]}")
        print(f"    Scaled features: {scaled_features[:3]}")

if __name__ == "__main__":
    debug_model()
