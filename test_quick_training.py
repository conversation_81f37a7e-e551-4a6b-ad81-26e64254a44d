#!/usr/bin/env python3
"""
Quick test of neural training with real data - just 5 epochs to verify it works
"""

import asyncio
import logging
from src.neural_cortex.enhanced_player_props_pipeline import EnhancedPlayerPropsConfig, EnhancedPlayerPropsPipeline

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_quick_training():
    """Test training with real data for just a few epochs"""
    
    logger.info("🎯 Quick Training Test with Real WNBA Data")
    logger.info("=" * 50)
    
    # Enhanced configuration for quick test
    config = EnhancedPlayerPropsConfig(
        prop_type="points",
        league="WNBA",
        
        # Quick training settings
        num_epochs=5,  # Just 5 epochs for testing
        batch_size=16,
        learning_rate=0.001,
        early_stopping_patience=10,
        
        # Model architecture
        hidden_dims=[256, 128, 64],  # Smaller model for quick test
        dropout_rate=0.2,
        
        # Enhanced features
        add_interaction_features=True,
        feature_selection_threshold=0.03,
        max_features=50,
        
        # Loss function
        loss_function="huber",
        huber_delta=1.5,
        
        # Ensemble (just 1 for quick test)
        ensemble_size=1,
        
        # Data augmentation
        noise_augmentation=True,
        augmentation_ratio=0.1,
        noise_std=0.05,
        
        # Model save
        model_save_path="models/quick_test_points.pt"
    )
    
    logger.info(f"📋 Quick Test Configuration:")
    logger.info(f"   Epochs: {config.num_epochs}")
    logger.info(f"   Model: {config.hidden_dims}")
    logger.info(f"   Features: {config.max_features} max")
    logger.info(f"   Loss: {config.loss_function}")
    
    # Create pipeline
    pipeline = EnhancedPlayerPropsPipeline(config)
    
    # Override target scaling to prevent regression-to-mean
    def no_target_scaling(self):
        """Override to disable target scaling"""
        logger.info("🎯 Target scaling DISABLED - using raw target values")
        return None, None
    
    # Monkey patch the target scaling method
    original_setup = pipeline._setup_target_scaling
    pipeline._setup_target_scaling = lambda: no_target_scaling(pipeline)
    
    logger.info("🚀 Starting quick training test...")
    
    try:
        result = await pipeline.train()
        
        if result:
            logger.info("✅ Quick training test completed successfully!")
            logger.info(f"📊 Final validation MAE: {result.get('val_mae', 'N/A')}")
            logger.info(f"📊 Final test MAE: {result.get('test_mae', 'N/A')}")
            
            # Check if predictions look reasonable
            if 'predictions_sample' in result:
                preds = result['predictions_sample']
                logger.info(f"📊 Sample predictions: {preds[:5]}")
        else:
            logger.error("❌ Training returned no results")
            
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_quick_training())
