#!/usr/bin/env python3
"""
🔍 INSPECT MODEL ARCHITECTURE
============================

This script inspects the actual architecture of the saved models
to understand the mismatch between expected and actual layer names.
"""

import torch
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def inspect_checkpoint(model_path: str):
    """Inspect a single checkpoint to understand its structure"""
    
    logger.info(f"🔍 Inspecting: {model_path}")
    
    if not Path(model_path).exists():
        logger.error(f"❌ Model file not found: {model_path}")
        return
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        
        logger.info("📋 Checkpoint keys:")
        for key in checkpoint.keys():
            logger.info(f"  - {key}")
        
        if 'model_state_dict' in checkpoint:
            logger.info("\n🧠 Model state dict keys:")
            state_dict = checkpoint['model_state_dict']
            
            # Group by layer type
            main_network_keys = [k for k in state_dict.keys() if k.startswith('main_network')]
            calibration_keys = [k for k in state_dict.keys() if k.startswith('calibration')]
            network_keys = [k for k in state_dict.keys() if k.startswith('network') and not k.startswith('main_network')]
            other_keys = [k for k in state_dict.keys() if not any(k.startswith(prefix) for prefix in ['main_network', 'calibration', 'network'])]
            
            if main_network_keys:
                logger.info(f"  📊 main_network layers ({len(main_network_keys)}):")
                for key in main_network_keys[:10]:  # Show first 10
                    logger.info(f"    - {key}")
                if len(main_network_keys) > 10:
                    logger.info(f"    ... and {len(main_network_keys) - 10} more")
            
            if calibration_keys:
                logger.info(f"  🎯 calibration layers ({len(calibration_keys)}):")
                for key in calibration_keys:
                    logger.info(f"    - {key}")
            
            if network_keys:
                logger.info(f"  🔗 network layers ({len(network_keys)}):")
                for key in network_keys[:10]:  # Show first 10
                    logger.info(f"    - {key}")
                if len(network_keys) > 10:
                    logger.info(f"    ... and {len(network_keys) - 10} more")
            
            if other_keys:
                logger.info(f"  ❓ other layers ({len(other_keys)}):")
                for key in other_keys:
                    logger.info(f"    - {key}")
        
        if 'config' in checkpoint:
            logger.info(f"\n⚙️ Model config:")
            config = checkpoint['config']
            for key, value in config.items():
                logger.info(f"  - {key}: {value}")
        
        if 'feature_list' in checkpoint:
            feature_list = checkpoint['feature_list']
            logger.info(f"\n📊 Features ({len(feature_list)}):")
            for i, feature in enumerate(feature_list):
                logger.info(f"  {i+1:2d}. {feature}")
        
        if 'feature_scaler_params' in checkpoint:
            scaler_params = checkpoint['feature_scaler_params']
            logger.info(f"\n🔧 Scaler params:")
            for key, value in scaler_params.items():
                if isinstance(value, list):
                    logger.info(f"  - {key}: list of {len(value)} items")
                else:
                    logger.info(f"  - {key}: {value}")
        
        logger.info("\n" + "="*60)
        
    except Exception as e:
        logger.error(f"❌ Error inspecting {model_path}: {e}")

def main():
    """Main inspection function"""
    
    logger.info("🔍 INSPECTING MODEL ARCHITECTURES")
    logger.info("="*60)
    
    # Inspect all 6 models
    model_paths = [
        "models/points_stat_specific_fixed_alt.pt",
        "models/rebounds_stat_specific_alt.pt",
        "models/assists_stat_specific_alt.pt",
        "models/steals_stat_specific_alt.pt",
        "models/blocks_stat_specific_alt.pt",
        "models/threes_stat_specific_alt.pt"
    ]
    
    for model_path in model_paths:
        inspect_checkpoint(model_path)
        print()  # Add spacing between models

if __name__ == "__main__":
    main()
