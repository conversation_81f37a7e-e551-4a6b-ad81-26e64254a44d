#!/usr/bin/env python3
"""
Quick script to examine the enhanced model features
"""

import torch
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def examine_model(model_path):
    """Examine model checkpoint"""
    try:
        logger.info(f"🔍 Examining model: {model_path}")
        checkpoint = torch.load(model_path, map_location='cpu')
        
        logger.info(f"📋 Checkpoint keys: {list(checkpoint.keys())}")
        
        if 'feature_list' in checkpoint:
            feature_list = checkpoint['feature_list']
            logger.info(f"✅ Feature list found with {len(feature_list)} features:")
            for i, feature in enumerate(feature_list):
                logger.info(f"   {i+1:2d}. {feature}")
        else:
            logger.info("❌ No feature_list found in checkpoint")
            
        if 'feature_scaler_params' in checkpoint:
            scaler_params = checkpoint['feature_scaler_params']
            logger.info(f"📊 Scaler params keys: {list(scaler_params.keys())}")
            if 'n_features_in_' in scaler_params:
                logger.info(f"   Features expected: {scaler_params['n_features_in_']}")
        else:
            logger.info("❌ No feature_scaler_params found")
            
        # Check model state dict for input size
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
            first_layer_key = None
            for key in state_dict.keys():
                if 'weight' in key and ('0.' in key or 'main_network.0.' in key):
                    first_layer_key = key
                    break
            
            if first_layer_key:
                first_layer_weight = state_dict[first_layer_key]
                input_size = first_layer_weight.shape[1]
                logger.info(f"🔢 Model input size from first layer: {input_size}")
                logger.info(f"   First layer shape: {first_layer_weight.shape}")
            
        return checkpoint
        
    except Exception as e:
        logger.error(f"❌ Error examining model: {e}")
        return None

if __name__ == "__main__":
    # Try different model files
    model_files = [
        "models/points_enhanced_model.pt",
        "models/points_enhanced_model_fixed.pt", 
        "models/enhanced_points_production.pt"
    ]
    
    for model_file in model_files:
        try:
            examine_model(model_file)
            print("\n" + "="*60 + "\n")
        except Exception as e:
            logger.error(f"❌ Could not examine {model_file}: {e}")
