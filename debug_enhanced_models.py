#!/usr/bin/env python3
"""
🔍 DEBUG ENHANCED MODELS
=======================

Diagnose the scaling/prediction issues in enhanced models
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.preprocessing import RobustScaler
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleEnhancedModel(nn.Module):
    """Simple enhanced model architecture"""
    
    def __init__(self, input_dim=54, hidden_dims=[256, 256, 128], dropout_rate=0.3, use_batch_norm=True):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            if dropout_rate > 0:
                layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim
        
        self.main_network = nn.Sequential(*layers)
        self.calibration = nn.Linear(prev_dim, 1)
    
    def forward(self, x):
        x = self.main_network(x)
        x = self.calibration(x)
        return x

def analyze_model_checkpoint(model_path: str, stat_name: str):
    """Analyze model checkpoint for debugging"""
    
    logger.info(f"\n🔍 ANALYZING {stat_name.upper()} MODEL")
    logger.info("="*50)
    
    try:
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # Analyze checkpoint contents
        logger.info(f"📦 Checkpoint keys: {list(checkpoint.keys())}")
        
        # Model config
        model_config = checkpoint.get('model_config', {})
        logger.info(f"🏗️ Model config: {model_config}")
        
        # Training results
        training_results = checkpoint.get('training_results', {})
        logger.info(f"📊 Training results: {training_results}")
        
        # Feature scaler parameters
        scaler_params = checkpoint.get('feature_scaler_params', {})
        logger.info(f"⚖️ Scaler parameters available: {list(scaler_params.keys())}")
        
        if 'center_' in scaler_params and 'scale_' in scaler_params:
            center = np.array(scaler_params['center_'])
            scale = np.array(scaler_params['scale_'])
            logger.info(f"📏 Scaler center shape: {center.shape}, scale shape: {scale.shape}")
            logger.info(f"📏 Scaler center range: [{center.min():.3f}, {center.max():.3f}]")
            logger.info(f"📏 Scaler scale range: [{scale.min():.3f}, {scale.max():.3f}]")
            
            # Check for extreme scaling values
            if np.any(scale < 1e-6) or np.any(scale > 1e6):
                logger.warning("⚠️ EXTREME SCALING VALUES DETECTED!")
                extreme_indices = np.where((scale < 1e-6) | (scale > 1e6))[0]
                logger.warning(f"   Extreme scale indices: {extreme_indices}")
                logger.warning(f"   Extreme scale values: {scale[extreme_indices]}")
        
        # Feature list
        feature_list = checkpoint.get('feature_list', [])
        logger.info(f"📋 Feature list length: {len(feature_list)}")
        if len(feature_list) > 0:
            logger.info(f"📋 First 10 features: {feature_list[:10]}")
        
        # Load and test model
        model = SimpleEnhancedModel(**model_config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Test with zero input (should give bias)
        zero_input = torch.zeros(1, model_config['input_dim'])
        with torch.no_grad():
            zero_prediction = model(zero_input).item()
        logger.info(f"🎯 Zero input prediction (bias): {zero_prediction:.3f}")
        
        # Test with small input
        small_input = torch.ones(1, model_config['input_dim']) * 0.1
        with torch.no_grad():
            small_prediction = model(small_input).item()
        logger.info(f"🎯 Small input prediction: {small_prediction:.3f}")
        
        # Test with realistic basketball features (unscaled)
        if stat_name.lower() == 'points':
            realistic_features = np.array([
                # Base stats: points, rebounds, assists, steals, blocks, threes
                20.0, 5.0, 3.0, 1.0, 0.5, 2.0,
                # Fill rest with reasonable values
                *([1.0] * (54 - 6))
            ]).reshape(1, -1)
        else:  # rebounds
            realistic_features = np.array([
                # Base stats: points, rebounds, assists, steals, blocks, threes
                15.0, 8.0, 2.0, 1.0, 1.0, 1.0,
                # Fill rest with reasonable values
                *([1.0] * (54 - 6))
            ]).reshape(1, -1)
        
        # Test without scaling
        realistic_tensor = torch.FloatTensor(realistic_features)
        with torch.no_grad():
            unscaled_prediction = model(realistic_tensor).item()
        logger.info(f"🎯 Realistic unscaled prediction: {unscaled_prediction:.3f}")
        
        # Test with scaling if available
        if 'center_' in scaler_params and 'scale_' in scaler_params:
            scaler = RobustScaler()
            scaler.center_ = center
            scaler.scale_ = scale
            scaler.n_features_in_ = 54
            
            try:
                realistic_scaled = scaler.transform(realistic_features)
                realistic_scaled_tensor = torch.FloatTensor(realistic_scaled)
                
                with torch.no_grad():
                    scaled_prediction = model(realistic_scaled_tensor).item()
                logger.info(f"🎯 Realistic scaled prediction: {scaled_prediction:.3f}")
                
                # Check if scaling is the issue
                scaling_ratio = scaled_prediction / unscaled_prediction if unscaled_prediction != 0 else float('inf')
                logger.info(f"📊 Scaling effect ratio: {scaling_ratio:.3f}")
                
                # Analyze scaled features
                logger.info(f"📊 Scaled features range: [{realistic_scaled.min():.3f}, {realistic_scaled.max():.3f}]")
                logger.info(f"📊 Scaled features mean: {realistic_scaled.mean():.3f}")
                
            except Exception as e:
                logger.error(f"❌ Error applying scaler: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error analyzing {stat_name} model: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_data_ranges():
    """Test the training data ranges used in enhanced pipeline"""
    
    logger.info(f"\n🔍 ANALYZING TRAINING DATA RANGES")
    logger.info("="*50)
    
    # Recreate the validation data used for training
    validation_data = [
        {"player": "Kelsey Mitchell", "points": 28, "rebounds": 2},
        {"player": "Dearica Hamby", "points": 21, "rebounds": 11},
        {"player": "Napheesa Collier", "points": 21, "rebounds": 6},
        {"player": "Aari McDonald", "points": 18, "rebounds": 2},
        {"player": "Aliyah Boston", "points": 18, "rebounds": 8},
        {"player": "Caitlin Clark", "points": 16, "rebounds": 4},
        {"player": "Kia Vaughn", "points": 16, "rebounds": 8},
        {"player": "Kayla McBride", "points": 19, "rebounds": 3},
        {"player": "NaLyssa Smith", "points": 12, "rebounds": 6},
        {"player": "Layshia Clarendon", "points": 12, "rebounds": 3},
        {"player": "Rickea Jackson", "points": 11, "rebounds": 4},
        {"player": "Erica Wheeler", "points": 8, "rebounds": 1},
        {"player": "Li Yueru", "points": 6, "rebounds": 4},
        {"player": "Lexie Hull", "points": 5, "rebounds": 1},
        {"player": "Stephanie Talbot", "points": 3, "rebounds": 1},
        {"player": "Azura Stevens", "points": 2, "rebounds": 2}
    ]
    
    df = pd.DataFrame(validation_data)
    
    logger.info(f"📊 POINTS TRAINING DATA:")
    logger.info(f"   Range: [{df['points'].min()}, {df['points'].max()}]")
    logger.info(f"   Mean: {df['points'].mean():.2f}")
    logger.info(f"   Std: {df['points'].std():.2f}")
    
    logger.info(f"📊 REBOUNDS TRAINING DATA:")
    logger.info(f"   Range: [{df['rebounds'].min()}, {df['rebounds'].max()}]")
    logger.info(f"   Mean: {df['rebounds'].mean():.2f}")
    logger.info(f"   Std: {df['rebounds'].std():.2f}")
    
    # With 10x augmentation, the targets would be repeated 10 times
    points_targets = np.repeat(df['points'].values, 10)
    rebounds_targets = np.repeat(df['rebounds'].values, 10)
    
    logger.info(f"📊 AUGMENTED POINTS TARGETS (160 samples):")
    logger.info(f"   Range: [{points_targets.min()}, {points_targets.max()}]")
    logger.info(f"   Mean: {points_targets.mean():.2f}")
    logger.info(f"   Std: {points_targets.std():.2f}")
    
    logger.info(f"📊 AUGMENTED REBOUNDS TARGETS (160 samples):")
    logger.info(f"   Range: [{rebounds_targets.min()}, {rebounds_targets.max()}]")
    logger.info(f"   Mean: {rebounds_targets.mean():.2f}")
    logger.info(f"   Std: {rebounds_targets.std():.2f}")

def main():
    """Main debugging function"""
    
    logger.info("🔍 ENHANCED MODEL DEBUGGING")
    logger.info("="*60)
    
    # Test training data ranges
    test_training_data_ranges()
    
    # Analyze both models
    models = [
        ('models/points_enhanced_model.pt', 'Points'),
        ('models/rebounds_enhanced_model.pt', 'Rebounds')
    ]
    
    for model_path, stat_name in models:
        if Path(model_path).exists():
            analyze_model_checkpoint(model_path, stat_name)
        else:
            logger.error(f"❌ Model not found: {model_path}")
    
    logger.info(f"\n🎯 DEBUGGING SUMMARY:")
    logger.info("   Check for extreme scaling values in scaler parameters")
    logger.info("   Compare zero input vs realistic input predictions")
    logger.info("   Verify training data ranges match expected basketball values")

if __name__ == "__main__":
    main()
