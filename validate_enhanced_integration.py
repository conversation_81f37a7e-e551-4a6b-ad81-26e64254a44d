#!/usr/bin/env python3
"""
🎯 VALIDATE ENHANCED POINTS MODEL INTEGRATION
============================================

Direct validation that the enhanced points model is properly integrated.
"""

import os
from pathlib import Path
import torch
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_integration():
    """Validate the enhanced points model integration"""
    
    logger.info("🎯 VALIDATING ENHANCED POINTS MODEL INTEGRATION")
    logger.info("=" * 60)
    
    # Check if enhanced model exists
    enhanced_model_path = "models/enhanced_basketball_models/best_points_model.pt"
    
    if not Path(enhanced_model_path).exists():
        logger.error(f"❌ Enhanced model not found: {enhanced_model_path}")
        return False
    
    logger.info(f"✅ Enhanced model found: {enhanced_model_path}")
    
    # Load and examine the model
    try:
        checkpoint = torch.load(enhanced_model_path, map_location='cpu')
        
        # Check key components
        has_feature_list = 'feature_list' in checkpoint
        has_config = 'config' in checkpoint
        has_scalers = 'feature_scaler_params' in checkpoint and 'target_scaler_params' in checkpoint
        
        logger.info(f"📊 Model components:")
        logger.info(f"   Feature list: {'✅' if has_feature_list else '❌'}")
        logger.info(f"   Config: {'✅' if has_config else '❌'}")
        logger.info(f"   Scalers: {'✅' if has_scalers else '❌'}")
        
        if has_feature_list:
            feature_list = checkpoint['feature_list']
            logger.info(f"   Features: {len(feature_list)}")
            
            # Check for enhanced features
            enhanced_features = ['usage_rate', 'recent_points_avg_5', 'field_goal_attempts']
            has_enhanced = all(feat in feature_list for feat in enhanced_features)
            logger.info(f"   Enhanced features: {'✅' if has_enhanced else '❌'}")
            
            if has_enhanced:
                logger.info("🎯 This is the enhanced points model!")
                return True
            else:
                logger.warning("⚠️ This appears to be a standard model")
                return False
        else:
            logger.error("❌ No feature list found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error loading model: {e}")
        return False

def check_service_integration():
    """Check if the service is configured to use enhanced model"""
    
    logger.info("\n🔍 CHECKING SERVICE INTEGRATION")
    logger.info("=" * 50)
    
    try:
        # Check the service file
        service_file = "src/services/unified_neural_prediction_service.py"
        
        if not Path(service_file).exists():
            logger.error(f"❌ Service file not found: {service_file}")
            return False
        
        with open(service_file, 'r') as f:
            content = f.read()
        
        # Check for enhanced model integration
        checks = {
            'Enhanced path': 'enhanced_basketball_models' in content,
            'Enhanced method': '_predict_with_enhanced_points_model' in content,
            'Feature creation': '_create_enhanced_points_features' in content,
            'Mixed scale comment': 'MIXED SCALE format' in content
        }
        
        logger.info("📊 Service integration checks:")
        for check_name, passed in checks.items():
            logger.info(f"   {check_name}: {'✅' if passed else '❌'}")
        
        all_passed = all(checks.values())
        
        if all_passed:
            logger.info("✅ Service is properly configured for enhanced model!")
            return True
        else:
            logger.warning("⚠️ Service integration incomplete")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking service: {e}")
        return False

def test_feature_creation():
    """Test the enhanced feature creation logic"""
    
    logger.info("\n🧪 TESTING FEATURE CREATION")
    logger.info("=" * 40)
    
    try:
        # Import the service
        import sys
        sys.path.append('.')
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
        
        service = UnifiedNeuralPredictionService()
        
        # Test player data
        player_data = {
            'name': 'A\'ja Wilson',
            'points': 18.0,
            'rebounds': 10.0,
            'assists': 0.0,
            'steals': 1.0,
            'blocks': 2.0,
            'threes': 0.0,
            'minutes_per_game': 34.0,
            'games_played': 30,
            'usage_rate': 28.0,
            'field_goal_attempts': 16.0,
            'field_goal_percentage': 0.563
        }
        
        # Mock feature list (40 features like enhanced model)
        feature_list = [
            'points', 'rebounds', 'assists', 'steals', 'blocks', 'threes',
            'games_played', 'minutes_per_game', 'field_goal_percentage', 'free_throw_percentage',
            'age', 'usage_rate', 'field_goal_attempts', 'three_point_attempts', 'free_throw_attempts',
            'recent_points_avg_5', 'recent_points_avg_10', 'team_pace', 'opponent_def_rating',
            'home_game', 'starter_status'
        ] + [f'feature_{i}' for i in range(21, 41)]  # Pad to 40 features
        
        # Test feature creation
        features = service._create_enhanced_points_features(player_data, feature_list)
        
        logger.info(f"📊 Feature creation test:")
        logger.info(f"   Features created: {len(features)}")
        logger.info(f"   Expected: {len(feature_list)}")
        logger.info(f"   Match: {'✅' if len(features) == len(feature_list) else '❌'}")
        
        # Check key values
        if len(features) >= 8:
            points_val = features[0]  # Should be per-game
            minutes_val = features[7]  # Should be total season
            
            logger.info(f"   Points value: {points_val:.1f} (per-game)")
            logger.info(f"   Minutes value: {minutes_val:.0f} (total season)")
            
            # Validate mixed scale
            expected_total_minutes = player_data['minutes_per_game'] * player_data['games_played']
            mixed_scale_correct = (abs(points_val - player_data['points']) < 0.1 and 
                                 abs(minutes_val - expected_total_minutes) < 1.0)
            
            logger.info(f"   Mixed scale format: {'✅' if mixed_scale_correct else '❌'}")
            
            if mixed_scale_correct:
                logger.info("✅ Feature creation working correctly!")
                return True
            else:
                logger.warning("⚠️ Mixed scale format incorrect")
                return False
        else:
            logger.error("❌ Insufficient features created")
            return False
            
    except Exception as e:
        logger.error(f"❌ Feature creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main validation function"""
    
    logger.info("🎯 ENHANCED POINTS MODEL INTEGRATION VALIDATION")
    logger.info("=" * 70)
    
    # Run all validation checks
    model_valid = validate_integration()
    service_valid = check_service_integration()
    feature_valid = test_feature_creation()
    
    logger.info(f"\n📋 VALIDATION SUMMARY")
    logger.info("=" * 30)
    logger.info(f"Model validation: {'✅' if model_valid else '❌'}")
    logger.info(f"Service integration: {'✅' if service_valid else '❌'}")
    logger.info(f"Feature creation: {'✅' if feature_valid else '❌'}")
    
    all_valid = model_valid and service_valid and feature_valid
    
    if all_valid:
        logger.info("\n🎉 ALL VALIDATIONS PASSED!")
        logger.info("✅ Enhanced points model is properly integrated!")
        logger.info("🚀 Ready for real-world testing!")
    else:
        logger.info("\n⚠️ SOME VALIDATIONS FAILED")
        logger.info("❌ Integration needs fixes before testing")
    
    return all_valid

if __name__ == "__main__":
    main()
