#!/usr/bin/env python3
"""
🔍 DEBUG POINTS TRAINING DATA
============================

Analyze the training data for points model to understand under-prediction
"""

import sys
import os
sys.path.append('.')

import numpy as np
import pandas as pd
from enhanced_player_props_pipeline import EnhancedTrainingPipeline, create_validation_data_for_training
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_points_training_data():
    """Analyze the training data for points model"""
    
    logger.info("🔍 ANALYZING POINTS TRAINING DATA")
    logger.info("="*50)
    
    # Create validation data
    validation_df = create_validation_data_for_training()
    logger.info(f"📊 Base validation data: {len(validation_df)} examples")
    
    # Show sample of validation data
    logger.info("\n📋 SAMPLE VALIDATION DATA:")
    for i, row in validation_df.head(3).iterrows():
        logger.info(f"   Player {i+1}: {row['points']:.1f} points, {row['rebounds']:.1f} rebounds, {row['assists']:.1f} assists")
    
    # Create enhanced training data for points
    pipeline = EnhancedTrainingPipeline('points')
    X, y, feature_names = pipeline.create_enhanced_training_data(validation_df)
    
    logger.info(f"\n📊 ENHANCED TRAINING DATA ANALYSIS:")
    logger.info(f"   Training samples: {X.shape[0]}")
    logger.info(f"   Features: {X.shape[1]}")
    logger.info(f"   Target (points) stats:")
    logger.info(f"      Mean: {np.mean(y):.2f}")
    logger.info(f"      Std: {np.std(y):.2f}")
    logger.info(f"      Min: {np.min(y):.1f}")
    logger.info(f"      Max: {np.max(y):.1f}")
    logger.info(f"      Median: {np.median(y):.1f}")
    
    # Check distribution
    percentiles = [10, 25, 50, 75, 90]
    logger.info(f"\n📊 POINTS DISTRIBUTION:")
    for p in percentiles:
        value = np.percentile(y, p)
        logger.info(f"   {p}th percentile: {value:.1f} points")
    
    # Check if data is realistic
    realistic_min, realistic_max = 5, 35
    realistic_count = np.sum((y >= realistic_min) & (y <= realistic_max))
    realistic_pct = realistic_count / len(y) * 100
    
    logger.info(f"\n🎯 REALISM CHECK:")
    logger.info(f"   Expected range: [{realistic_min}, {realistic_max}] points")
    logger.info(f"   Samples in realistic range: {realistic_count}/{len(y)} ({realistic_pct:.1f}%)")
    
    # Check for outliers
    low_outliers = np.sum(y < realistic_min)
    high_outliers = np.sum(y > realistic_max)
    
    logger.info(f"   Low outliers (<{realistic_min}): {low_outliers}")
    logger.info(f"   High outliers (>{realistic_max}): {high_outliers}")
    
    # Show some specific examples
    logger.info(f"\n📋 SAMPLE TRAINING TARGETS:")
    sample_indices = np.random.choice(len(y), min(10, len(y)), replace=False)
    for i, idx in enumerate(sample_indices):
        logger.info(f"   Sample {i+1}: {y[idx]:.1f} points")
    
    # Check feature ranges
    logger.info(f"\n📊 FEATURE ANALYSIS:")
    logger.info(f"   Feature means range: [{np.mean(X, axis=0).min():.3f}, {np.mean(X, axis=0).max():.3f}]")
    logger.info(f"   Feature stds range: [{np.std(X, axis=0).min():.3f}, {np.std(X, axis=0).max():.3f}]")
    
    # Check for potential issues
    issues = []
    
    if np.mean(y) < 10:
        issues.append("⚠️ Very low mean points (suggests conservative training data)")
    
    if np.std(y) < 3:
        issues.append("⚠️ Low variance in points (limited diversity)")
    
    if realistic_pct < 80:
        issues.append("⚠️ Many unrealistic point values in training data")
    
    if low_outliers > len(y) * 0.2:
        issues.append("⚠️ Too many low-scoring examples")
    
    logger.info(f"\n🚨 POTENTIAL ISSUES:")
    if issues:
        for issue in issues:
            logger.info(f"   {issue}")
    else:
        logger.info("   ✅ No obvious issues detected")
    
    return {
        'mean': np.mean(y),
        'std': np.std(y),
        'min': np.min(y),
        'max': np.max(y),
        'realistic_pct': realistic_pct,
        'issues': issues,
        'X': X,
        'y': y,
        'feature_names': feature_names
    }

def main():
    """Main analysis function"""
    
    try:
        analysis = analyze_points_training_data()
        
        logger.info(f"\n🎯 SUMMARY:")
        logger.info(f"   Training data mean: {analysis['mean']:.2f} points")
        logger.info(f"   Training data range: [{analysis['min']:.1f}, {analysis['max']:.1f}]")
        logger.info(f"   Realistic samples: {analysis['realistic_pct']:.1f}%")
        logger.info(f"   Issues found: {len(analysis['issues'])}")
        
        if analysis['mean'] < 10:
            logger.info("\n💡 RECOMMENDATION: Training data has very low mean points")
            logger.info("   This explains why model predicts 1-4 points instead of 10-30")
            logger.info("   Need to adjust training data or add realistic examples")
        
    except Exception as e:
        logger.error(f"❌ Error in analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
