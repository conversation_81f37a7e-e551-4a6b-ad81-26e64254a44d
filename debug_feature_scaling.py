#!/usr/bin/env python3
"""
Debug feature scaling issues
"""

import pandas as pd
import numpy as np
import torch
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def compare_features():
    """Compare training features vs validation features"""
    
    # Load training data
    training_data = pd.read_csv("data/real_wnba_points_training_data.csv")
    logger.info(f"📊 Training data shape: {training_data.shape}")
    
    # Load validation data
    validation_data = pd.read_csv("data/complete_real_wnba_features_with_metadata.csv")
    
    # Convert validation to per-game
    total_stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    for stat in total_stats:
        if stat in validation_data.columns and 'games_played' in validation_data.columns:
            validation_data[stat] = validation_data[stat] / validation_data['games_played'].replace(0, 1)
    
    logger.info(f"📊 Validation data shape: {validation_data.shape}")
    
    # Compare key stats
    logger.info("\n🔍 TRAINING DATA STATS:")
    for stat in ['points', 'rebounds', 'assists', 'minutes_per_game']:
        if stat in training_data.columns:
            logger.info(f"   {stat}: mean={training_data[stat].mean():.2f}, std={training_data[stat].std():.2f}, range=[{training_data[stat].min():.2f}, {training_data[stat].max():.2f}]")
    
    logger.info("\n🔍 VALIDATION DATA STATS:")
    for stat in ['points', 'rebounds', 'assists', 'minutes_per_game']:
        if stat in validation_data.columns:
            logger.info(f"   {stat}: mean={validation_data[stat].mean():.2f}, std={validation_data[stat].std():.2f}, range=[{validation_data[stat].min():.2f}, {validation_data[stat].max():.2f}]")
    
    # Check model scaler
    logger.info("\n🔍 MODEL SCALER PARAMETERS:")
    checkpoint = torch.load("models/points_enhanced_model.pt", map_location='cpu')
    scaler_params = checkpoint['feature_scaler_params']
    
    feature_list = checkpoint['feature_list']
    logger.info(f"📋 Feature list length: {len(feature_list)}")
    
    # Check first few features
    for i, feature in enumerate(feature_list[:10]):
        if 'center_' in scaler_params:
            mean_val = scaler_params['center_'][i]
            scale_val = scaler_params['scale_'][i]
        else:
            mean_val = scaler_params['mean_'][i]
            scale_val = scaler_params['scale_'][i]
        logger.info(f"   {feature}: mean={mean_val:.4f}, scale={scale_val:.4f}")
    
    # Test a simple prediction
    logger.info("\n🔍 TESTING SIMPLE PREDICTION:")
    
    # Create a simple feature vector (A'ja Wilson-like stats)
    simple_features = np.zeros(54)
    simple_features[0] = 19.1  # points per game
    simple_features[1] = 8.3   # rebounds per game
    simple_features[2] = 2.0   # assists per game
    simple_features[7] = 28.3  # minutes per game
    
    logger.info(f"Raw features (first 10): {simple_features[:10]}")
    
    # Apply scaling
    if 'center_' in scaler_params:
        scaled_features = (simple_features - scaler_params['center_']) / scaler_params['scale_']
    else:
        scaled_features = (simple_features - scaler_params['mean_']) / scaler_params['scale_']
    
    logger.info(f"Scaled features (first 10): {scaled_features[:10]}")
    
    # Load model and predict
    from real_wnba_validation_pipeline import EnhancedNeuralNetwork
    model = EnhancedNeuralNetwork(54, [256, 256, 128], 0.3, True)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    with torch.no_grad():
        prediction = model(torch.FloatTensor(scaled_features).unsqueeze(0))
        logger.info(f"Raw prediction: {prediction.item():.2f}")

if __name__ == "__main__":
    compare_features()
