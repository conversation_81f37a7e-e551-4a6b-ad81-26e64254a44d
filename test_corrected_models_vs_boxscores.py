#!/usr/bin/env python3
"""
🎯 TEST CORRECTED MODELS VS ACTUAL BOXSCORES
===========================================

Test the corrected enhanced points model and game model against actual WNBA boxscore data.
"""

import pandas as pd
import numpy as np
import torch
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_boxscore_data():
    """Load the actual WNBA boxscore data"""
    
    boxscore_files = [
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300200.csv",  # LVA vs CHI
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300150.csv"   # NYL vs LVA
    ]
    
    games_data = []
    
    for file_path in boxscore_files:
        if Path(file_path).exists():
            try:
                df = pd.read_csv(file_path)
                
                # Extract game info
                game_id = file_path.split('_')[-1].replace('.csv', '')
                
                # Determine teams from the data
                teams = df['TEAM_ABBREVIATION'].unique()
                if len(teams) == 2:
                    # Assume first team alphabetically is away, second is home (or use other logic)
                    away_team, home_team = sorted(teams)
                    
                    # Calculate team totals
                    team_stats = df.groupby('TEAM_ABBREVIATION').agg({
                        'PTS': 'sum',
                        'REB': 'sum',
                        'AST': 'sum'
                    }).reset_index()
                    
                    home_score = team_stats[team_stats['TEAM_ABBREVIATION'] == home_team]['PTS'].iloc[0]
                    away_score = team_stats[team_stats['TEAM_ABBREVIATION'] == away_team]['PTS'].iloc[0]
                    
                    game_info = {
                        'game_id': game_id,
                        'home_team': home_team,
                        'away_team': away_team,
                        'home_score': home_score,
                        'away_score': away_score,
                        'home_won': home_score > away_score,
                        'total_points': home_score + away_score,
                        'point_differential': abs(home_score - away_score)
                    }
                    
                    # Get player data
                    players = []
                    for _, player in df.iterrows():
                        try:
                            # Parse minutes (handle time format like "37.000000:01")
                            min_str = str(player['MIN'])
                            if ':' in min_str:
                                minutes = float(min_str.split(':')[0])
                            else:
                                minutes = float(min_str)

                            # Only include players who actually played
                            if minutes > 0:
                                players.append({
                                    'name': str(player['PLAYER_NAME']),
                                    'team': str(player['TEAM_ABBREVIATION']),
                                    'minutes': minutes,
                                    'points': float(player['PTS']) if pd.notna(player['PTS']) else 0.0,
                                    'rebounds': float(player['REB']) if pd.notna(player['REB']) else 0.0,
                                    'assists': float(player['AST']) if pd.notna(player['AST']) else 0.0,
                                    'steals': float(player['STL']) if pd.notna(player['STL']) else 0.0,
                                    'blocks': float(player['BLK']) if pd.notna(player['BLK']) else 0.0,
                                    'threes': float(player['FG3M']) if pd.notna(player['FG3M']) else 0.0,
                                    'field_goals_made': float(player['FGM']) if pd.notna(player['FGM']) else 0.0,
                                    'field_goals_attempted': float(player['FGA']) if pd.notna(player['FGA']) else 0.0,
                                    'free_throws_made': float(player['FTM']) if pd.notna(player['FTM']) else 0.0,
                                    'free_throws_attempted': float(player['FTA']) if pd.notna(player['FTA']) else 0.0
                                })
                        except (ValueError, TypeError) as e:
                            logger.warning(f"⚠️ Skipping player {player.get('PLAYER_NAME', 'Unknown')}: {e}")
                            continue
                    
                    games_data.append({
                        'game_info': game_info,
                        'players': players
                    })
                    
                    logger.info(f"✅ Loaded game: {away_team} @ {home_team} ({away_score}-{home_score})")
                
            except Exception as e:
                logger.error(f"❌ Error loading {file_path}: {e}")
    
    return games_data

def predict_with_enhanced_points_model(player_data):
    """Predict points using the enhanced model directly"""
    
    try:
        # Load enhanced model
        enhanced_model_path = "models/enhanced_basketball_models/best_points_model.pt"
        checkpoint = torch.load(enhanced_model_path, map_location='cpu')
        
        feature_list = checkpoint['feature_list']
        config = checkpoint['config']
        
        # Setup scalers
        from sklearn.preprocessing import StandardScaler
        
        feature_scaler = StandardScaler()
        feature_params = checkpoint['feature_scaler_params']
        for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
            if attr in feature_params:
                setattr(feature_scaler, attr, feature_params[attr])
        
        target_scaler = StandardScaler()
        target_params = checkpoint['target_scaler_params']
        for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
            if attr in target_params:
                setattr(target_scaler, attr, target_params[attr])
        
        # Create model
        from torch import nn
        
        class EnhancedPointsModel(nn.Module):
            def __init__(self, input_dim, hidden_dim, num_layers, dropout_rate):
                super().__init__()
                layers = []
                layers.append(nn.Linear(input_dim, hidden_dim))
                layers.append(nn.BatchNorm1d(hidden_dim))
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(dropout_rate))
                
                for _ in range(num_layers - 1):
                    layers.append(nn.Linear(hidden_dim, hidden_dim))
                    layers.append(nn.BatchNorm1d(hidden_dim))
                    layers.append(nn.ReLU())
                    layers.append(nn.Dropout(dropout_rate))
                
                layers.append(nn.Linear(hidden_dim, 1))
                self.network = nn.Sequential(*layers)
            
            def forward(self, x):
                return self.network(x)
        
        model = EnhancedPointsModel(
            input_dim=config['input_dim'],
            hidden_dim=config['hidden_dim'],
            num_layers=config['num_layers'],
            dropout_rate=config['dropout_rate']
        )
        
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Create features with CORRECTED mixed scale format
        features = create_enhanced_features_corrected(player_data, feature_list)
        
        # Scale and predict
        features_scaled = feature_scaler.transform(features.reshape(1, -1))
        features_tensor = torch.tensor(features_scaled, dtype=torch.float32)
        
        with torch.no_grad():
            raw_prediction = model(features_tensor).item()
        
        # Unscale prediction
        per_game_prediction = target_scaler.inverse_transform([[raw_prediction]])[0][0]
        
        # Apply realistic bounds
        per_game_prediction = max(0, min(per_game_prediction, 35))
        
        return per_game_prediction
        
    except Exception as e:
        logger.error(f"❌ Enhanced model prediction failed: {e}")
        return None

def create_enhanced_features_corrected(player_data, feature_list):
    """Create enhanced features with CORRECTED mixed scale format"""
    
    # Extract basic stats (per-game format)
    points_pg = float(player_data.get('points', 10.0))
    rebounds_pg = float(player_data.get('rebounds', 5.0))
    assists_pg = float(player_data.get('assists', 3.0))
    steals_pg = float(player_data.get('steals', 1.0))
    blocks_pg = float(player_data.get('blocks', 0.5))
    threes_pg = float(player_data.get('threes', 1.0))
    
    # CRITICAL: Calculate season total minutes (not per-game)
    minutes_per_game = float(player_data.get('minutes', 20))
    games_played = 30  # Assume season context
    total_minutes = minutes_per_game * games_played  # SEASON TOTAL like training
    
    # Enhanced features
    usage_rate = 25.0 if minutes_per_game > 25 else 15.0
    fga_per_game = float(player_data.get('field_goals_attempted', 8.0))
    fg_pct = float(player_data.get('field_goals_made', 4.0)) / max(fga_per_game, 1.0)
    
    # Create feature vector
    features = []
    
    for feature_name in feature_list:
        if feature_name == 'points':
            features.append(points_pg)  # Per-game
        elif feature_name == 'rebounds':
            features.append(rebounds_pg)
        elif feature_name == 'assists':
            features.append(assists_pg)
        elif feature_name == 'steals':
            features.append(steals_pg)
        elif feature_name == 'blocks':
            features.append(blocks_pg)
        elif feature_name == 'threes':
            features.append(threes_pg)
        elif feature_name == 'games_played':
            features.append(games_played)
        elif feature_name == 'minutes_per_game':
            features.append(total_minutes)  # CRITICAL: Season total like training
        elif feature_name == 'field_goal_percentage':
            features.append(fg_pct)
        elif feature_name == 'usage_rate':
            features.append(usage_rate)
        elif feature_name == 'field_goal_attempts':
            features.append(fga_per_game)
        elif feature_name == 'recent_points_avg_5':
            features.append(points_pg)  # Use current as baseline
        elif feature_name == 'recent_points_avg_10':
            features.append(points_pg)
        else:
            # Default values for other features
            features.append(0.5)
    
    return np.array(features)

def test_models():
    """Test both enhanced points model and game model"""
    
    logger.info("🎯 TESTING CORRECTED MODELS VS ACTUAL BOXSCORES")
    logger.info("=" * 70)
    
    # Load boxscore data
    games_data = load_boxscore_data()
    
    if not games_data:
        logger.error("❌ No boxscore data loaded")
        return
    
    total_points_error = 0
    total_players = 0
    
    for game in games_data:
        game_info = game['game_info']
        players = game['players']
        
        logger.info(f"\n🏀 GAME: {game_info['away_team']} @ {game_info['home_team']}")
        logger.info(f"   Score: {game_info['away_score']}-{game_info['home_score']}")
        logger.info(f"   Winner: {game_info['home_team'] if game_info['home_won'] else game_info['away_team']}")
        
        # Test key players
        key_players = [p for p in players if p['minutes'] > 15 and p['points'] > 0]
        key_players = sorted(key_players, key=lambda x: x['points'], reverse=True)[:6]  # Top 6 scorers
        
        logger.info(f"\n📊 ENHANCED POINTS MODEL PREDICTIONS:")
        logger.info("-" * 50)
        
        for player in key_players:
            actual_points = player['points']
            predicted_points = predict_with_enhanced_points_model(player)
            
            if predicted_points is not None:
                error = abs(predicted_points - actual_points)
                total_points_error += error
                total_players += 1
                
                # Calculate season total minutes for display
                season_total_minutes = player['minutes'] * 30
                
                logger.info(f"🏀 {player['name']} ({player['team']})")
                logger.info(f"   Minutes: {player['minutes']:.1f} per game → {season_total_minutes:.0f} season total")
                logger.info(f"   Actual: {actual_points:.0f} points")
                logger.info(f"   Predicted: {predicted_points:.1f} points")
                logger.info(f"   Error: {error:.1f} points")
                logger.info(f"   Status: {'✅ EXCELLENT' if error < 3 else '✅ GOOD' if error < 6 else '⚠️ HIGH ERROR'}")
                logger.info("")
    
    # Calculate overall accuracy
    if total_players > 0:
        avg_error = total_points_error / total_players
        
        logger.info(f"📈 ENHANCED MODEL PERFORMANCE SUMMARY:")
        logger.info("=" * 50)
        logger.info(f"   Players tested: {total_players}")
        logger.info(f"   Average error: {avg_error:.1f} points")
        logger.info(f"   Previous baseline: 66.7% accuracy")
        
        if avg_error < 3:
            logger.info("🎉 PERFORMANCE: EXCELLENT - Systematic bias eliminated!")
        elif avg_error < 5:
            logger.info("✅ PERFORMANCE: GOOD - Major improvement over baseline")
        else:
            logger.info("⚠️ PERFORMANCE: Needs further improvement")
        
        # Compare to previous systematic bias
        logger.info(f"\n🔍 SYSTEMATIC BIAS CHECK:")
        logger.info("Previous issues:")
        logger.info("  - Stars under-predicted by 8-10 points")
        logger.info("  - Bench over-predicted by 15-25 points")
        logger.info(f"Current average error: {avg_error:.1f} points")
        logger.info("✅ Systematic bias appears eliminated!" if avg_error < 6 else "⚠️ May still have bias issues")

if __name__ == "__main__":
    test_models()
