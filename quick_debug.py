#!/usr/bin/env python3
"""
Quick debug of enhanced models
"""

import torch
import numpy as np

print("🔍 QUICK DEBUG - Enhanced Models")
print("="*50)

# Check if models exist
import os
models = ['models/points_enhanced_model.pt', 'models/rebounds_enhanced_model.pt']

for model_path in models:
    if os.path.exists(model_path):
        print(f"✅ Found: {model_path}")
        
        try:
            # Load checkpoint
            checkpoint = torch.load(model_path, map_location='cpu')
            print(f"   Keys: {list(checkpoint.keys())}")
            
            # Check training results
            training_results = checkpoint.get('training_results', {})
            print(f"   Training MAE: {training_results.get('mae', 'N/A')}")
            print(f"   Training R²: {training_results.get('r2', 'N/A')}")
            
            # Check scaler params
            scaler_params = checkpoint.get('feature_scaler_params', {})
            if 'center_' in scaler_params:
                center = np.array(scaler_params['center_'])
                scale = np.array(scaler_params['scale_'])
                print(f"   Scaler center range: [{center.min():.3f}, {center.max():.3f}]")
                print(f"   Scaler scale range: [{scale.min():.3f}, {scale.max():.3f}]")
                
                # Check for problematic scaling
                if np.any(scale < 1e-6):
                    print(f"   ⚠️ VERY SMALL SCALE VALUES: {scale[scale < 1e-6]}")
                if np.any(scale > 1e6):
                    print(f"   ⚠️ VERY LARGE SCALE VALUES: {scale[scale > 1e6]}")
            
        except Exception as e:
            print(f"   ❌ Error loading: {e}")
    else:
        print(f"❌ Missing: {model_path}")

print("\n🎯 DIAGNOSIS:")
print("   Looking for extreme scaling values that could cause unrealistic predictions")
