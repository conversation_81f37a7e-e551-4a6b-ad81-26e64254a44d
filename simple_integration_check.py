#!/usr/bin/env python3
"""
🎯 SIMPLE INTEGRATION CHECK
==========================

Basic validation without complex imports.
"""

import os
from pathlib import Path
import torch

def check_integration():
    """Check integration status"""
    
    print("🎯 ENHANCED POINTS MODEL INTEGRATION CHECK")
    print("=" * 60)
    
    # 1. Check enhanced model exists
    enhanced_model_path = "models/enhanced_basketball_models/best_points_model.pt"
    model_exists = Path(enhanced_model_path).exists()
    print(f"1. Enhanced model exists: {'✅' if model_exists else '❌'}")
    
    if model_exists:
        try:
            checkpoint = torch.load(enhanced_model_path, map_location='cpu')
            feature_list = checkpoint.get('feature_list', [])
            has_enhanced_features = len(feature_list) == 40 and 'usage_rate' in feature_list
            print(f"   - 40 features with enhanced: {'✅' if has_enhanced_features else '❌'}")
        except:
            print(f"   - Model loading: ❌")
    
    # 2. Check service file integration
    service_file = "src/services/unified_neural_prediction_service.py"
    service_exists = Path(service_file).exists()
    print(f"2. Service file exists: {'✅' if service_exists else '❌'}")
    
    if service_exists:
        with open(service_file, 'r') as f:
            content = f.read()
        
        checks = {
            'Enhanced path added': 'enhanced_basketball_models' in content,
            'Enhanced prediction method': '_predict_with_enhanced_points_model' in content,
            'Feature creation method': '_create_enhanced_points_features' in content,
            'Mixed scale handling': 'MIXED SCALE format' in content,
            'Total minutes logic': 'total_minutes = minutes_per_game * games_played' in content
        }
        
        for check_name, passed in checks.items():
            print(f"   - {check_name}: {'✅' if passed else '❌'}")
    
    # 3. Check training data
    training_file = "data/real_wnba_points_training_data.csv"
    training_exists = Path(training_file).exists()
    print(f"3. Training data exists: {'✅' if training_exists else '❌'}")
    
    # 4. Check boxscore data
    boxscore_files = [
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300200.csv",
        "data/smart_10year_historical/boxscore_traditional_v2_wnba_game_id_1022300150.csv"
    ]
    
    boxscore_count = sum(1 for f in boxscore_files if Path(f).exists())
    print(f"4. Boxscore data available: {boxscore_count}/2 files ({'✅' if boxscore_count > 0 else '❌'})")
    
    print(f"\n📋 INTEGRATION STATUS:")
    
    if model_exists and service_exists:
        print("✅ CORE INTEGRATION: Complete")
        print("🎯 Enhanced points model is integrated into UnifiedNeuralPredictionService")
        print("🔧 Mixed-scale feature format implemented")
        print("📊 Ready for testing against actual WNBA boxscore data")
        
        print(f"\n🚀 NEXT STEPS:")
        print("1. Test enhanced model predictions vs actual boxscore data")
        print("2. Validate systematic bias is resolved (stars vs bench players)")
        print("3. Compare accuracy vs baseline 66.7%")
        print("4. Deploy for live game predictions")
        
        return True
    else:
        print("❌ INTEGRATION: Incomplete")
        return False

if __name__ == "__main__":
    check_integration()
