#!/usr/bin/env python3
"""
Debug Model Saving Issue
Test single model training with detailed debugging
"""

import asyncio
import logging
import os
import torch
from src.neural_cortex.enhanced_player_props_pipeline import EnhancedPlayerPropsTrainingPipeline, EnhancedPlayerPropsConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_single_model():
    """Debug single model training and saving"""
    
    logger.info("🔍 DEBUGGING MODEL SAVE ISSUE")
    logger.info("=" * 50)

    # First, test basic torch.save functionality
    try:
        test_data = {"test": "data", "number": 42}
        test_path = os.path.abspath("models/torch_save_test.pt")
        logger.info(f"🧪 Testing basic torch.save with: {test_path}")

        torch.save(test_data, test_path)

        if os.path.exists(test_path):
            file_size = os.path.getsize(test_path)
            logger.info(f"✅ Basic torch.save works! File size: {file_size} bytes")

            # Test loading
            loaded_data = torch.load(test_path)
            logger.info(f"✅ Basic torch.load works! Data: {loaded_data}")

            # Clean up
            os.remove(test_path)
        else:
            logger.error("❌ Basic torch.save failed - file not created")
            return

    except Exception as basic_error:
        logger.error(f"❌ Basic torch.save failed: {basic_error}")
        return

    try:
        # Test with points model (simplest case)
        # Try different save locations and extensions
        import time
        timestamp = int(time.time())

        # Test multiple save paths
        save_paths = [
            f"debug_points_test_{timestamp}.pt",  # Current directory
            f"debug_points_test_{timestamp}.pth",  # Different extension
            f"debug_points_test_{timestamp}.pkl",  # Pickle extension
        ]

        for i, test_path in enumerate(save_paths):
            logger.info(f"🧪 Testing save path {i+1}: {test_path}")
            try:
                test_data = {"test": f"model_data_{i}", "timestamp": timestamp}
                torch.save(test_data, test_path)

                if os.path.exists(test_path):
                    file_size = os.path.getsize(test_path)
                    logger.info(f"✅ Save path {i+1} works! File size: {file_size} bytes")
                    os.remove(test_path)  # Clean up
                    save_path = os.path.abspath(test_path)
                    break
                else:
                    logger.error(f"❌ Save path {i+1} failed - file not created")
            except Exception as path_error:
                logger.error(f"❌ Save path {i+1} failed: {path_error}")
        else:
            logger.error("❌ All save paths failed!")
            return

        config = EnhancedPlayerPropsConfig(
            league="WNBA",
            prop_type='points',

            # Simple architecture for debugging
            hidden_dim=128,
            num_layers=2,
            dropout_rate=0.3,
            use_batch_norm=False,

            # Short training for debugging
            num_epochs=5,
            learning_rate=0.001,
            batch_size=32,
            early_stopping_patience=3,

            # Basic features
            add_interaction_features=False,
            feature_selection_threshold=0.05,
            max_features=30,

            # Loss function
            loss_function="mse",

            # Model save with absolute path
            model_save_path=save_path
        )
        
        logger.info(f"🎯 Config created: {config.prop_type}")
        logger.info(f"📁 Save path: {config.model_save_path}")
        
        # Check if models directory exists
        models_dir = os.path.dirname(config.model_save_path)
        logger.info(f"📁 Models directory: {models_dir}")
        logger.info(f"📁 Directory exists: {os.path.exists(models_dir)}")
        
        if not os.path.exists(models_dir):
            os.makedirs(models_dir, exist_ok=True)
            logger.info(f"📁 Created models directory")
        
        # Initialize pipeline
        logger.info("🚀 Initializing pipeline...")
        pipeline = EnhancedPlayerPropsTrainingPipeline(config)
        
        # Train model
        logger.info("🎯 Starting training...")
        result = await pipeline.train()
        
        logger.info("✅ Training completed!")
        logger.info(f"   Result: {result}")
        
        # Check final file
        if os.path.exists(config.model_save_path):
            file_size = os.path.getsize(config.model_save_path)
            logger.info(f"📊 Final file size: {file_size} bytes")
            
            if file_size > 0:
                logger.info("🎉 SUCCESS: Model saved correctly!")
                
                # Test loading the model
                try:
                    loaded_data = torch.load(config.model_save_path)
                    logger.info(f"✅ Model loads successfully!")
                    logger.info(f"   Keys: {list(loaded_data.keys())}")
                except Exception as load_error:
                    logger.error(f"❌ Error loading saved model: {load_error}")
            else:
                logger.error("❌ FAILURE: Model file is still 0 bytes!")
        else:
            logger.error("❌ FAILURE: Model file was not created!")
            
    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR: {e}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_single_model())
