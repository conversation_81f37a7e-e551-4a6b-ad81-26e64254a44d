#!/usr/bin/env python3
"""
Debug the severe scaling issue with enhanced models
"""

import torch
import numpy as np
from enhanced_player_props_pipeline import EnhancedPlayerPropsModel

def debug_model_scaling():
    print("🔍 DEBUGGING SEVERE SCALING ISSUE")
    print("="*50)
    
    # Load points model
    print("📂 Loading points model...")
    checkpoint = torch.load('models/points_enhanced_model_FIXED_v2.pt', map_location='cpu')
    
    # Check scaler parameters
    scaler_params = checkpoint['feature_scaler_params']
    mean_array = np.array(scaler_params['mean_'])
    scale_array = np.array(scaler_params['scale_'])
    
    print(f"📊 Scaler Analysis:")
    print(f"   Mean array shape: {mean_array.shape}")
    print(f"   Scale array shape: {scale_array.shape}")
    print(f"   Mean range: [{mean_array.min():.3f}, {mean_array.max():.3f}]")
    print(f"   Scale range: [{scale_array.min():.6f}, {scale_array.max():.3f}]")
    
    # Check for problematic scaling
    zero_scales = np.sum(scale_array == 0)
    tiny_scales = np.sum(scale_array < 1e-6)
    large_scales = np.sum(scale_array > 100)
    
    print(f"   Zero scales: {zero_scales}")
    print(f"   Tiny scales (<1e-6): {tiny_scales}")
    print(f"   Large scales (>100): {large_scales}")
    
    if tiny_scales > 0:
        print("❌ FOUND TINY SCALES - This causes extreme amplification!")
        tiny_indices = np.where(scale_array < 1e-6)[0]
        print(f"   Tiny scale indices: {tiny_indices[:10]}...")  # Show first 10
        print(f"   Tiny scale values: {scale_array[tiny_indices[:10]]}")
    
    # Test with zero input (should give reasonable baseline)
    model = EnhancedPlayerPropsModel(
        input_dim=54,
        hidden_dims=[512, 256, 128, 64],
        dropout_rate=0.2,
        use_batch_norm=True
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Test with zero features
    zero_features = np.zeros(54)
    zero_scaled = (zero_features - mean_array) / scale_array
    
    print(f"\n🔍 Zero features scaling:")
    print(f"   Zero scaled range: [{zero_scaled.min():.3f}, {zero_scaled.max():.3f}]")
    print(f"   Zero scaled mean: {zero_scaled.mean():.3f}")
    
    # Check for extreme values in scaled features
    extreme_count = np.sum(np.abs(zero_scaled) > 100)
    print(f"   Extreme scaled values (>100): {extreme_count}")
    
    if extreme_count > 0:
        extreme_indices = np.where(np.abs(zero_scaled) > 100)[0]
        print(f"   Extreme indices: {extreme_indices[:5]}...")
        print(f"   Extreme values: {zero_scaled[extreme_indices[:5]]}")
    
    # Make prediction with zero features
    with torch.no_grad():
        zero_tensor = torch.FloatTensor(zero_scaled).unsqueeze(0)
        baseline_pred = model(zero_tensor).squeeze().item()
    
    print(f"\n🎯 Baseline prediction: {baseline_pred:.1f}")
    
    # Expected baseline should be around training mean (13.5 for points)
    if abs(baseline_pred) > 100:
        print("❌ BASELINE PREDICTION UNREALISTIC - Severe scaling issue confirmed")
    else:
        print("✅ Baseline prediction reasonable")
    
    # Check training results from checkpoint
    if 'training_results' in checkpoint:
        training_results = checkpoint['training_results']
        print(f"\n📊 Training Results:")
        print(f"   Final MAE: {training_results.get('final_mae', 'N/A')}")
        print(f"   R² Score: {training_results.get('r2_score', 'N/A')}")
    
    return tiny_scales > 0, extreme_count > 0

def suggest_fix():
    print("\n🔧 SUGGESTED FIXES:")
    print("1. Retrain with proper StandardScaler (no tiny scale values)")
    print("2. Add scale clipping: scale = np.maximum(scale, 1e-3)")
    print("3. Use RobustScaler with proper quantile range")
    print("4. Check feature engineering for constant/near-constant features")

if __name__ == "__main__":
    has_tiny_scales, has_extreme_values = debug_model_scaling()
    
    if has_tiny_scales or has_extreme_values:
        print("\n❌ CRITICAL SCALING ISSUES DETECTED")
        suggest_fix()
    else:
        print("\n✅ No obvious scaling issues found")
        print("🔍 Issue may be in feature generation or model architecture")
