#!/usr/bin/env python3
"""
Train a Single Model for Debugging

This script trains just the points model to debug the save issue.
"""

import os
import sys
import logging
import torch

# Add src to path
sys.path.append('src')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__main__)

def train_points_model():
    """Train just the points model"""
    try:
        # Import the enhanced pipeline
        from neural_cortex.enhanced_player_props_pipeline import EnhancedPlayerPropsConfig, EnhancedPlayerPropsPipeline
        
        logger.info("🚀 Training POINTS model...")
        
        # Create config
        config = EnhancedPlayerPropsConfig(
            league="WNBA",
            prop_type="points",
            hidden_dim=256,
            num_layers=4,
            dropout_rate=0.4,
            use_batch_norm=False,  # Fixed BatchNorm issue
            num_epochs=5,  # Shorter for debugging
            batch_size=64,
            model_save_path="models/debug_points_model.pt"
        )
        
        logger.info(f"📁 Model will be saved to: {config.model_save_path}")
        
        # Initialize pipeline
        pipeline = EnhancedPlayerPropsPipeline(config)
        
        # Train the model
        logger.info("🎯 Starting training...")
        result = pipeline.train()
        
        logger.info(f"✅ Training completed!")
        logger.info(f"📊 Result: {result}")
        
        # Check if model was saved
        if os.path.exists(config.model_save_path):
            file_size = os.path.getsize(config.model_save_path)
            logger.info(f"✅ Model saved successfully: {file_size:,} bytes")
            
            # Try to load it back
            try:
                checkpoint = torch.load(config.model_save_path, map_location='cpu')
                logger.info(f"✅ Model loads successfully")
                logger.info(f"📊 Checkpoint keys: {list(checkpoint.keys())}")
                return True
            except Exception as load_error:
                logger.error(f"❌ Failed to load saved model: {load_error}")
                return False
        else:
            logger.error(f"❌ Model file not found: {config.model_save_path}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Training failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution"""
    logger.info("🏀 HYPER MEDUSA - Single Model Training Debug")
    logger.info("=" * 50)
    
    success = train_points_model()
    
    if success:
        logger.info("🚀 SUCCESS! Model training and saving works correctly")
    else:
        logger.error("❌ FAILED! There's an issue with model training or saving")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
