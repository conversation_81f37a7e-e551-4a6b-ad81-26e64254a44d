#!/usr/bin/env python3
"""
Comprehensive test script to validate all code quality fixes:
1. Duplicate fields removal in PlayerPropsConfig
2. Negative prediction clamping
3. Type safety in contextual adjustments
4. Early stopping parameter consistency
5. Feature list consistency
"""

import sys
import os
import asyncio
import logging
from typing import Dict, Any, List
import torch
import pandas as pd

# Add project root to path
sys.path.append('.')

from src.neural_cortex.player_props_neural_pipeline import PlayerPropsConfig
from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_duplicate_fields_removal():
    """Test 1: Verify duplicate fields are removed from PlayerPropsConfig"""
    logger.info("🧪 TEST 1: Checking PlayerPropsConfig for duplicate fields...")
    
    try:
        # Create a PlayerPropsConfig instance
        config = PlayerPropsConfig(prop_type="points", league="WNBA")
        
        # Get all field names
        field_names = [field.name for field in config.__dataclass_fields__.values()]
        
        # Check for duplicates
        duplicates = []
        seen = set()
        for name in field_names:
            if name in seen:
                duplicates.append(name)
            seen.add(name)
        
        if duplicates:
            logger.error(f"❌ DUPLICATE FIELDS FOUND: {duplicates}")
            return False
        else:
            logger.info("✅ No duplicate fields found in PlayerPropsConfig")
            
        # Verify specific fields exist only once
        critical_fields = ['hidden_dim', 'num_layers', 'dropout_rate']
        for field in critical_fields:
            count = field_names.count(field)
            if count != 1:
                logger.error(f"❌ Field '{field}' appears {count} times (should be 1)")
                return False
            else:
                logger.info(f"✅ Field '{field}' appears exactly once")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 1 failed: {e}")
        return False

def test_negative_prediction_clamping():
    """Test 2: Verify negative predictions are clamped to zero"""
    logger.info("🧪 TEST 2: Testing negative prediction clamping...")
    
    try:
        service = UnifiedNeuralPredictionService(league="WNBA")
        
        # Test various stat types with negative inputs
        test_cases = [
            ("points", -5.2),
            ("rebounds", -1.3),
            ("assists", -0.8),
            ("steals", -0.13),  # This was the specific case mentioned
            ("blocks", -0.5),
            ("threes", -2.1)
        ]
        
        all_passed = True
        for stat_type, negative_value in test_cases:
            clamped_value = service._clamp_prediction_to_realistic_bounds(negative_value, stat_type)
            
            if clamped_value < 0:
                logger.error(f"❌ {stat_type}: {negative_value} → {clamped_value} (still negative!)")
                all_passed = False
            else:
                logger.info(f"✅ {stat_type}: {negative_value} → {clamped_value} (properly clamped)")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Test 2 failed: {e}")
        return False

def test_type_safety_contextual_adjustments():
    """Test 3: Verify type safety in contextual adjustments"""
    logger.info("🧪 TEST 3: Testing type safety in contextual adjustments...")
    
    try:
        service = UnifiedNeuralPredictionService(league="WNBA")
        
        # Test with string tier values (the problematic case)
        test_players = [
            {"name": "A'ja Wilson", "tier": "superstar", "position": "F"},
            {"name": "Test Player", "tier": "elite", "position": "G"},
            {"name": "Bench Player", "tier": "bench", "position": "F"},
            {"name": "Numeric Tier", "tier": 2, "position": "C"},  # Numeric tier
            {"name": "None Tier", "tier": None, "position": "G"}   # None tier
        ]
        
        all_passed = True
        for player_data in test_players:
            try:
                # This should not raise any type comparison errors
                adjusted_prediction = service._apply_contextual_adjustments(
                    prediction=15.0, 
                    player_data=player_data, 
                    prop_type="points"
                )
                
                if isinstance(adjusted_prediction, (int, float)) and adjusted_prediction > 0:
                    logger.info(f"✅ {player_data['name']} (tier: {player_data['tier']}): {adjusted_prediction:.1f}")
                else:
                    logger.error(f"❌ Invalid adjustment result for {player_data['name']}: {adjusted_prediction}")
                    all_passed = False
                    
            except Exception as e:
                logger.error(f"❌ Type error for {player_data['name']}: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ Test 3 failed: {e}")
        return False

def test_early_stopping_parameter_consistency():
    """Test 4: Verify early stopping parameter consistency"""
    logger.info("🧪 TEST 4: Testing early stopping parameter consistency...")
    
    try:
        # Check PlayerPropsConfig uses early_stopping_patience (inherited from TrainingConfig)
        config = PlayerPropsConfig(prop_type="points", league="WNBA")
        
        # Verify early_stopping_patience exists
        if hasattr(config, 'early_stopping_patience'):
            logger.info(f"✅ early_stopping_patience found: {config.early_stopping_patience}")
        else:
            logger.error("❌ early_stopping_patience not found in PlayerPropsConfig")
            return False
        
        # Verify no conflicting 'patience' parameter
        if hasattr(config, 'patience'):
            logger.warning(f"⚠️ Found conflicting 'patience' parameter: {config.patience}")
            # This might be acceptable if it's used for a different purpose
        else:
            logger.info("✅ No conflicting 'patience' parameter found")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 4 failed: {e}")
        return False

async def test_comprehensive_prediction_bounds():
    """Test 5: Comprehensive prediction bounds testing"""
    logger.info("🧪 TEST 5: Testing comprehensive prediction bounds...")
    
    try:
        service = UnifiedNeuralPredictionService(league="WNBA")
        
        # Test player data with realistic stats
        test_player = {
            "name": "A'ja Wilson",
            "points_per_game": 22.8,
            "rebounds_per_game": 9.4,
            "assists_per_game": 2.3,
            "steals_per_game": 1.1,
            "blocks_per_game": 2.6,
            "threes_per_game": 0.8,
            "tier": "superstar",
            "position": "F"
        }
        
        # Test game data
        test_game = {
            "home_team": "Las Vegas Aces",
            "away_team": "New York Liberty",
            "date": "2024-07-06"
        }
        
        # Make predictions
        result = await service.predict_unified(test_game, [test_player])
        
        if result and result.player_props:
            player_props = result.player_props.get("A'ja Wilson", {})
            
            all_valid = True
            for stat_type, prediction in player_props.items():
                # Check if prediction is non-negative
                if prediction < 0:
                    logger.error(f"❌ {stat_type}: {prediction} (negative prediction!)")
                    all_valid = False
                else:
                    logger.info(f"✅ {stat_type}: {prediction:.1f} (valid)")
            
            return all_valid
        else:
            logger.warning("⚠️ No predictions returned - service may not be fully loaded")
            return True  # Don't fail the test if models aren't available
        
    except Exception as e:
        logger.error(f"❌ Test 5 failed: {e}")
        return False

async def main():
    """Run all code quality tests"""
    logger.info("🚀 Starting comprehensive code quality validation tests...")
    
    tests = [
        ("Duplicate Fields Removal", test_duplicate_fields_removal),
        ("Negative Prediction Clamping", test_negative_prediction_clamping),
        ("Type Safety in Contextual Adjustments", test_type_safety_contextual_adjustments),
        ("Early Stopping Parameter Consistency", test_early_stopping_parameter_consistency),
        ("Comprehensive Prediction Bounds", test_comprehensive_prediction_bounds)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL CODE QUALITY FIXES VALIDATED SUCCESSFULLY!")
        return True
    else:
        logger.error(f"💥 {total - passed} tests failed - code quality issues remain")
        return False

if __name__ == "__main__":
    asyncio.run(main())
