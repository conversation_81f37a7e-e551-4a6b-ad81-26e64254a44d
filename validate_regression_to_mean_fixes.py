#!/usr/bin/env python3
"""
🎯 VALIDATE REGRESSION-TO-MEAN FIXES
===================================

Comprehensive validation of the fixes for the regression-to-mean issue:
1. Test prediction spread preservation
2. Validate star player vs role player predictions
3. Check negative prediction clamping
4. Analyze feature importance for outlier detection
"""

import sys
import os
import asyncio
import logging
import numpy as np
import pandas as pd
import torch
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def validate_regression_to_mean_fixes():
    """Comprehensive validation of regression-to-mean fixes"""
    
    logger.info("🎯 VALIDATING REGRESSION-TO-MEAN FIXES")
    logger.info("=" * 60)
    
    try:
        # Initialize the unified service
        service = UnifiedNeuralPredictionService(league="WNBA")
        
        # Test data from actual WNBA games with known outcomes
        test_game = {
            "home_team": "Las Vegas Aces",
            "away_team": "New York Liberty", 
            "date": "2024-07-06"
        }
        
        # Test players with diverse performance levels
        test_players = [
            {
                "name": "A'ja Wilson",
                "points_per_game": 22.8,
                "rebounds_per_game": 9.4,
                "assists_per_game": 2.3,
                "steals_per_game": 1.1,
                "blocks_per_game": 2.6,
                "threes_per_game": 0.8,
                "tier": "superstar",
                "position": "F",
                "actual_points": 21.0,
                "expected_range": (18, 26)  # Expected prediction range
            },
            {
                "name": "Kelsey Plum",
                "points_per_game": 17.8,
                "rebounds_per_game": 2.6,
                "assists_per_game": 4.2,
                "steals_per_game": 1.0,
                "blocks_per_game": 0.2,
                "threes_per_game": 2.8,
                "tier": "elite",
                "position": "G",
                "actual_points": 17.0,
                "expected_range": (14, 21)
            },
            {
                "name": "Chelsea Gray",
                "points_per_game": 12.3,
                "rebounds_per_game": 3.1,
                "assists_per_game": 6.2,
                "steals_per_game": 1.2,
                "blocks_per_game": 0.3,
                "threes_per_game": 1.4,
                "tier": "solid",
                "position": "G",
                "actual_points": 12.0,
                "expected_range": (9, 15)
            },
            {
                "name": "Bench Player",
                "points_per_game": 6.2,
                "rebounds_per_game": 2.1,
                "assists_per_game": 1.8,
                "steals_per_game": 0.5,
                "blocks_per_game": 0.1,
                "threes_per_game": 0.8,
                "tier": "bench",
                "position": "G",
                "actual_points": 8.0,
                "expected_range": (4, 10)
            }
        ]
        
        # Make predictions
        logger.info("🔮 Making predictions...")
        result = await service.predict_unified(test_game, test_players)
        
        if not result or not result.player_props:
            logger.error("❌ No predictions returned from service")
            return False
        
        # Analyze results
        predictions = []
        actuals = []
        errors = []
        
        logger.info("\n📊 PREDICTION ANALYSIS:")
        logger.info("-" * 60)
        
        for player in test_players:
            player_name = player["name"]
            actual = player["actual_points"]
            expected_min, expected_max = player["expected_range"]
            
            if player_name in result.player_props:
                predicted = result.player_props[player_name].get("points", 0)
                error = abs(predicted - actual)
                
                predictions.append(predicted)
                actuals.append(actual)
                errors.append(error)
                
                # Check if prediction is in expected range
                in_range = expected_min <= predicted <= expected_max
                range_status = "✅" if in_range else "⚠️"
                
                logger.info(f"{player_name}:")
                logger.info(f"   Tier: {player['tier']}")
                logger.info(f"   Season Avg: {player['points_per_game']:.1f}")
                logger.info(f"   Predicted: {predicted:.1f}")
                logger.info(f"   Actual: {actual:.1f}")
                logger.info(f"   Error: {error:.1f}")
                logger.info(f"   Expected Range: {expected_min}-{expected_max} {range_status}")
                logger.info("")
            else:
                logger.warning(f"❌ No prediction for {player_name}")
        
        # Overall analysis
        if predictions and actuals:
            predictions = np.array(predictions)
            actuals = np.array(actuals)
            errors = np.array(errors)
            
            logger.info("📈 OVERALL ANALYSIS:")
            logger.info("-" * 40)
            
            # Prediction spread analysis
            pred_spread = np.max(predictions) - np.min(predictions)
            actual_spread = np.max(actuals) - np.min(actuals)
            spread_ratio = pred_spread / (actual_spread + 1e-6)
            
            logger.info(f"Prediction Spread: {pred_spread:.1f}")
            logger.info(f"Actual Spread: {actual_spread:.1f}")
            logger.info(f"Spread Ratio: {spread_ratio:.3f}")
            
            if spread_ratio < 0.7:
                logger.warning("⚠️ REGRESSION TO MEAN DETECTED!")
                logger.warning("   Model predictions are too conservative")
            elif spread_ratio > 1.3:
                logger.warning("⚠️ OVER-DISPERSION DETECTED!")
                logger.warning("   Model predictions are too extreme")
            else:
                logger.info("✅ Good prediction spread preservation")
            
            # Error analysis
            mean_error = np.mean(errors)
            logger.info(f"Mean Absolute Error: {mean_error:.2f}")
            
            # Check for systematic bias
            star_players = ["A'ja Wilson", "Kelsey Plum"]
            role_players = ["Chelsea Gray", "Bench Player"]
            
            star_predictions = [predictions[i] for i, player in enumerate(test_players) 
                              if player["name"] in star_players and i < len(predictions)]
            star_actuals = [actuals[i] for i, player in enumerate(test_players) 
                          if player["name"] in star_players and i < len(actuals)]
            
            role_predictions = [predictions[i] for i, player in enumerate(test_players) 
                              if player["name"] in role_players and i < len(predictions)]
            role_actuals = [actuals[i] for i, player in enumerate(test_players) 
                          if player["name"] in role_players and i < len(actuals)]
            
            if star_predictions and star_actuals:
                star_bias = np.mean(star_predictions) - np.mean(star_actuals)
                logger.info(f"Star Player Bias: {star_bias:.2f} {'(underpredicting)' if star_bias < 0 else '(overpredicting)'}")
            
            if role_predictions and role_actuals:
                role_bias = np.mean(role_predictions) - np.mean(role_actuals)
                logger.info(f"Role Player Bias: {role_bias:.2f} {'(underpredicting)' if role_bias < 0 else '(overpredicting)'}")
            
            # Check for negative predictions
            negative_count = np.sum(predictions < 0)
            if negative_count > 0:
                logger.warning(f"⚠️ {negative_count} negative predictions detected!")
            else:
                logger.info("✅ No negative predictions")
            
            # Overall assessment
            logger.info("\n🎯 REGRESSION-TO-MEAN FIX ASSESSMENT:")
            logger.info("-" * 50)
            
            issues = []
            if spread_ratio < 0.7:
                issues.append("Regression to mean still present")
            if mean_error > 4.0:
                issues.append("High prediction errors")
            if negative_count > 0:
                issues.append("Negative predictions not clamped")
            
            if not issues:
                logger.info("✅ ALL FIXES WORKING CORRECTLY!")
                logger.info("   - Prediction spread preserved")
                logger.info("   - No systematic bias detected")
                logger.info("   - Negative predictions clamped")
                return True
            else:
                logger.warning("⚠️ ISSUES STILL PRESENT:")
                for issue in issues:
                    logger.warning(f"   - {issue}")
                return False
        
        else:
            logger.error("❌ No valid predictions to analyze")
            return False
            
    except Exception as e:
        logger.error(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main validation function"""
    success = await validate_regression_to_mean_fixes()
    
    if success:
        logger.info("\n🎉 REGRESSION-TO-MEAN FIXES VALIDATED SUCCESSFULLY!")
    else:
        logger.error("\n💥 REGRESSION-TO-MEAN FIXES NEED MORE WORK")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
