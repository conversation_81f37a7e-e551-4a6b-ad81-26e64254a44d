#!/usr/bin/env python3
"""
Quick test to verify real WNBA points data loading
"""

import sys
import os
sys.path.append('src')

from data.basketball_data_loader import BasketballDataLoader
import pandas as pd
import numpy as np

def test_real_data_loading():
    """Test that we're loading real basketball points data instead of synthetic zeros"""
    
    print("🔍 Testing real WNBA points data loading...")
    print("=" * 50)
    
    # Load data using the basketball data loader
    data_loader = BasketballDataLoader()
    raw_data = data_loader.load_training_data(league="WNBA")
    
    if raw_data is None or len(raw_data) == 0:
        print("❌ ERROR: No data loaded!")
        return False
    
    print(f"✅ Loaded {len(raw_data)} records")
    
    # Check if we have target column
    if 'target' not in raw_data.columns:
        print("❌ ERROR: No 'target' column found!")
        print(f"Available columns: {list(raw_data.columns)}")
        return False
    
    # Analyze target values
    targets = raw_data['target'].values
    target_mean = np.mean(targets)
    target_std = np.std(targets)
    target_min = np.min(targets)
    target_max = np.max(targets)
    
    print(f"🎯 Target Statistics:")
    print(f"   Mean: {target_mean:.3f}")
    print(f"   Std:  {target_std:.3f}")
    print(f"   Min:  {target_min:.3f}")
    print(f"   Max:  {target_max:.3f}")
    
    # Check for synthetic data (all zeros or very low variance)
    if target_std < 0.1:
        print("❌ ERROR: Target values have very low variance - likely synthetic data!")
        print(f"   First 10 targets: {targets[:10]}")
        return False
    
    # Check for realistic basketball points range
    if target_mean < 1.0 or target_mean > 30.0:
        print(f"⚠️  WARNING: Target mean ({target_mean:.3f}) outside typical WNBA points range (1-30)")
    
    if target_max > 50.0:
        print(f"⚠️  WARNING: Target max ({target_max:.3f}) unusually high for WNBA")
    
    # Calculate spread ratio (our key diagnostic)
    predicted_spread = 2.7  # From our previous model output
    actual_spread = target_std * 2  # Approximate 2-sigma spread
    spread_ratio = predicted_spread / actual_spread if actual_spread > 0 else float('inf')
    
    print(f"📊 Spread Analysis:")
    print(f"   Predicted spread: {predicted_spread:.3f}")
    print(f"   Actual spread:    {actual_spread:.3f}")
    print(f"   Spread ratio:     {spread_ratio:.3f}")
    
    if spread_ratio > 100:
        print("❌ ERROR: Spread ratio too high - indicates synthetic data!")
        return False
    elif spread_ratio > 5:
        print("⚠️  WARNING: Spread ratio high - may indicate data quality issues")
    else:
        print("✅ Spread ratio looks reasonable")
    
    # Show sample of actual data
    print(f"\n📋 Sample of loaded data:")
    print(raw_data[['target']].head(10))
    
    print(f"\n✅ SUCCESS: Real basketball points data loaded successfully!")
    print(f"   Data source appears to contain actual WNBA player statistics")
    print(f"   Target values in realistic range with proper variance")
    
    return True

if __name__ == "__main__":
    success = test_real_data_loading()
    if not success:
        sys.exit(1)
    print("\n🎉 All tests passed!")
