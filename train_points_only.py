#!/usr/bin/env python3
"""
Quick script to retrain ONLY the points model with original successful configuration
"""

import asyncio
import logging
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.neural_cortex.enhanced_player_props_pipeline import EnhancedPlayerPropsTrainingPipeline, create_enhanced_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
    ]
)
logger = logging.getLogger(__name__)

async def main():
    """Train points model with original successful configuration"""
    
    logger.info("🔧 TRAINING POINTS MODEL - Restoring R²=0.364 Performance")
    logger.info("=" * 60)
    
    try:
        # Create config with original successful settings for points
        config = create_enhanced_config(
            league="WNBA",
            prop_type="points",
            num_epochs=300,  # Original successful setting
            learning_rate=0.001,
            hidden_dim=256,
            num_layers=4,
            early_stopping_patience=15,
            model_save_path="models/points_stat_specific_fixed.pt"
        )
        
        logger.info(f"🎯 Configuration:")
        logger.info(f"   Epochs: {config.num_epochs}")
        logger.info(f"   Learning Rate: {config.learning_rate}")
        logger.info(f"   Hidden Dim: {config.hidden_dim}")
        logger.info(f"   Layers: {config.num_layers}")
        logger.info(f"   Save Path: {config.model_save_path}")
        
        # Initialize trainer
        trainer = EnhancedPlayerPropsTrainingPipeline(config, prop_type="points")
        
        # Train model
        logger.info("🚀 Starting training...")
        results = await trainer.train()
        
        logger.info("✅ POINTS MODEL TRAINING COMPLETED!")
        logger.info(f"   Test MAE: {results['test_mae']:.4f}")
        logger.info(f"   Test R²: {results['test_r2']:.4f}")
        
        if results['test_r2'] > 0.3:
            logger.info("🎉 SUCCESS! Points model R² restored to good performance!")
        else:
            logger.warning(f"⚠️ Points model R² still low: {results['test_r2']:.4f}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Error training points model: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
