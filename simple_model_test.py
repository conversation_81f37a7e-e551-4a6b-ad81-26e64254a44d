#!/usr/bin/env python3
"""
🎯 SIMPLE MODEL TEST
===================

Basic test of enhanced models without complex imports
"""

import torch
import torch.nn as nn
import numpy as np
import logging
from sklearn.preprocessing import RobustScaler

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleEnhancedModel(nn.Module):
    """Simple enhanced model architecture"""
    
    def __init__(self, input_dim=54, hidden_dims=[256, 256, 128], dropout_rate=0.3, use_batch_norm=True):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            if dropout_rate > 0:
                layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim
        
        self.main_network = nn.Sequential(*layers)
        self.calibration = nn.Linear(prev_dim, 1)
    
    def forward(self, x):
        x = self.main_network(x)
        x = self.calibration(x)
        return x

def test_enhanced_model(model_path: str, stat_name: str):
    """Test enhanced model"""
    
    logger.info(f"\n🎯 Testing {stat_name} Enhanced Model")
    logger.info("="*50)
    
    try:
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location='cpu')
        logger.info(f"✅ Loaded checkpoint from {model_path}")
        
        # Get info
        model_config = checkpoint.get('model_config', {})
        training_results = checkpoint.get('training_results', {})
        scaler_params = checkpoint.get('feature_scaler_params', {})
        
        logger.info(f"📊 Model Config: {model_config}")
        logger.info(f"📈 Training Results:")
        logger.info(f"   MAE: {training_results.get('mae', 'N/A')}")
        logger.info(f"   R²: {training_results.get('r2', 'N/A')}")
        
        # Create model
        model = SimpleEnhancedModel(**model_config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        logger.info("✅ Model loaded successfully")
        
        # Test with dummy data
        dummy_features = np.random.randn(3, 54)  # 3 samples, 54 features
        
        # Load scaler if available
        if 'center_' in scaler_params and 'scale_' in scaler_params:
            scaler = RobustScaler()
            scaler.center_ = np.array(scaler_params['center_'])
            scaler.scale_ = np.array(scaler_params['scale_'])
            scaler.n_features_in_ = 54
            
            dummy_features_scaled = scaler.transform(dummy_features)
            logger.info("✅ Scaler applied successfully")
        else:
            dummy_features_scaled = dummy_features
            logger.warning("⚠️ No scaler parameters found")
        
        # Make predictions
        with torch.no_grad():
            features_tensor = torch.FloatTensor(dummy_features_scaled)
            predictions = model(features_tensor)
            predictions_clamped = torch.clamp(predictions, min=0.0)
        
        logger.info(f"🎯 Test Predictions:")
        for i, pred in enumerate(predictions_clamped):
            logger.info(f"   Sample {i+1}: {pred.item():.3f}")
        
        # Test with realistic basketball data
        logger.info(f"\n🏀 Testing with realistic {stat_name} data:")
        
        if stat_name == "Points":
            realistic_data = [
                [28, 2, 5, 1, 0, 6] + [0.0] * 48,  # High scorer like Kelsey Mitchell
                [21, 11, 4, 1, 0, 1] + [0.0] * 48,  # Balanced like Dearica Hamby
                [12, 6, 1, 0, 0, 0] + [0.0] * 48   # Role player
            ]
        else:  # Rebounds
            realistic_data = [
                [28, 2, 5, 1, 0, 6] + [0.0] * 48,  # Guard
                [21, 11, 4, 1, 0, 1] + [0.0] * 48,  # Forward
                [12, 6, 1, 0, 0, 0] + [0.0] * 48   # Center
            ]
        
        realistic_features = np.array(realistic_data)
        
        if 'center_' in scaler_params:
            realistic_features_scaled = scaler.transform(realistic_features)
        else:
            realistic_features_scaled = realistic_features
        
        with torch.no_grad():
            realistic_tensor = torch.FloatTensor(realistic_features_scaled)
            realistic_predictions = model(realistic_tensor)
            realistic_predictions_clamped = torch.clamp(realistic_predictions, min=0.0)
        
        player_types = ["High Scorer", "Balanced Player", "Role Player"]
        for i, (pred, player_type) in enumerate(zip(realistic_predictions_clamped, player_types)):
            actual = realistic_data[i][0 if stat_name == "Points" else 1]
            error = abs(pred.item() - actual)
            logger.info(f"   {player_type}: Predicted {pred.item():.1f}, Actual {actual}, Error {error:.1f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing {stat_name} model: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    
    logger.info("🎯 ENHANCED MODEL VALIDATION TEST")
    logger.info("="*60)
    
    # Test both enhanced models
    models = [
        ('models/points_enhanced_model.pt', 'Points'),
        ('models/rebounds_enhanced_model.pt', 'Rebounds')
    ]
    
    success_count = 0
    for model_path, stat_name in models:
        if test_enhanced_model(model_path, stat_name):
            success_count += 1
    
    logger.info(f"\n✅ VALIDATION SUMMARY:")
    logger.info(f"   Successfully tested {success_count}/{len(models)} enhanced models")
    
    if success_count == len(models):
        logger.info("🎉 All enhanced models are working correctly!")
        logger.info("🚀 Ready for production validation against 7/5/25 games")
    else:
        logger.warning("⚠️ Some models failed validation")

if __name__ == "__main__":
    main()
