#!/usr/bin/env python3
"""
📊 MODEL ERROR ANALYSIS AND VISUALIZATION
=========================================

This script analyzes the validation errors from our 7/5/25 games to understand:
1. Why points model has high MAE (9.71)
2. Why rebounds model has moderate MAE (2.57)
3. Feature importance and correlation issues
4. Prediction patterns and biases

Key Analysis:
- Error distribution visualization
- Player-specific prediction patterns
- Feature correlation analysis
- Model bias identification
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
from typing import Dict, List, Tuple
import sys

# Add compatible model architecture
sys.path.append('.')
from compatible_model_architecture import predict_with_compatible_model

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelErrorAnalyzer:
    """Analyze model prediction errors and patterns"""
    
    def __init__(self):
        self.validation_data = self._create_validation_data()
        self.model_paths = {
            'points': 'models/points_stat_specific_fixed_alt.pt',
            'rebounds': 'models/rebounds_stat_specific_alt.pt',
            'assists': 'models/assists_stat_specific_alt.pt',
            'steals': 'models/steals_stat_specific_alt.pt',
            'blocks': 'models/blocks_stat_specific_alt.pt',
            'threes': 'models/threes_stat_specific_alt.pt'
        }
        
    def _create_validation_data(self) -> pd.DataFrame:
        """Create validation data from 7/5/25 games"""
        
        validation_data = [
            # Los Angeles Sparks @ Indiana Fever (Sparks won 89-87)
            {"player": "Dearica Hamby", "team": "LAS", "opponent": "IND", "points": 21, "rebounds": 11, "assists": 4, "steals": 1, "blocks": 0, "threes": 1},
            {"player": "Aari McDonald", "team": "LAS", "opponent": "IND", "points": 18, "rebounds": 2, "assists": 3, "steals": 1, "blocks": 0, "threes": 4},
            {"player": "Kia Vaughn", "team": "LAS", "opponent": "IND", "points": 16, "rebounds": 8, "assists": 1, "steals": 0, "blocks": 1, "threes": 0},
            {"player": "Layshia Clarendon", "team": "LAS", "opponent": "IND", "points": 12, "rebounds": 3, "assists": 6, "steals": 2, "blocks": 0, "threes": 2},
            {"player": "Rickea Jackson", "team": "LAS", "opponent": "IND", "points": 11, "rebounds": 4, "assists": 1, "steals": 0, "blocks": 0, "threes": 1},
            {"player": "Li Yueru", "team": "LAS", "opponent": "IND", "points": 6, "rebounds": 4, "assists": 0, "steals": 0, "blocks": 1, "threes": 0},
            {"player": "Stephanie Talbot", "team": "LAS", "opponent": "IND", "points": 3, "rebounds": 1, "assists": 1, "steals": 0, "blocks": 0, "threes": 1},
            {"player": "Azura Stevens", "team": "LAS", "opponent": "IND", "points": 2, "rebounds": 2, "assists": 0, "steals": 1, "blocks": 0, "threes": 0},
            
            {"player": "Kelsey Mitchell", "team": "IND", "opponent": "LAS", "points": 28, "rebounds": 2, "assists": 5, "steals": 1, "blocks": 0, "threes": 6},
            {"player": "Aliyah Boston", "team": "IND", "opponent": "LAS", "points": 18, "rebounds": 8, "assists": 3, "steals": 0, "blocks": 1, "threes": 0},
            {"player": "Caitlin Clark", "team": "IND", "opponent": "LAS", "points": 16, "rebounds": 4, "assists": 7, "steals": 1, "blocks": 0, "threes": 2},
            {"player": "NaLyssa Smith", "team": "IND", "opponent": "LAS", "points": 12, "rebounds": 6, "assists": 1, "steals": 0, "blocks": 0, "threes": 0},
            {"player": "Erica Wheeler", "team": "IND", "opponent": "LAS", "points": 8, "rebounds": 1, "assists": 2, "steals": 1, "blocks": 0, "threes": 2},
            {"player": "Lexie Hull", "team": "IND", "opponent": "LAS", "points": 5, "rebounds": 1, "assists": 0, "steals": 0, "blocks": 0, "threes": 1},
            
            # Golden State Valkyries @ Minnesota Lynx (Lynx won 82-71)
            {"player": "Napheesa Collier", "team": "MIN", "opponent": "GS", "points": 21, "rebounds": 6, "assists": 2, "steals": 1, "blocks": 1, "threes": 1},
            {"player": "Kayla McBride", "team": "MIN", "opponent": "GS", "points": 19, "rebounds": 3, "assists": 3, "steals": 2, "blocks": 0, "threes": 5}
        ]
        
        return pd.DataFrame(validation_data)
    
    def create_inference_features(self, player_data: Dict) -> pd.DataFrame:
        """Create inference features for a player (13-feature version)"""
        
        features = {
            'stat_value': player_data.get('points', 15.0),
            'rank_position': 25.0,
            'player_consistency': 0.75,
            'player_tier': 2.0,
            'position_encoded': 2.0,
            'opponent_strength': 0.6,
            'home_advantage': 0.0 if player_data.get('team') in ['LAS', 'GS'] else 1.0,
            'rest_days': 1.0,
            'back_to_back': 0.0,
            'season_progress': 0.7,
            'recent_form': player_data.get('points', 15.0),
            'hot_streak': 1.0 if player_data.get('points', 0) > 20 else 0.0,
            'cold_streak': 1.0 if player_data.get('points', 0) < 5 else 0.0
        }
        
        return pd.DataFrame([features])
    
    def collect_all_predictions(self) -> pd.DataFrame:
        """Collect predictions from all models for analysis"""
        
        logger.info("📊 Collecting predictions from all models...")
        
        results = []
        
        for _, player_data in self.validation_data.iterrows():
            player_result = {
                'player': player_data['player'],
                'team': player_data['team']
            }
            
            # Add actual values
            for stat in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
                player_result[f'actual_{stat}'] = player_data[stat]
            
            # Get predictions from each model
            input_df = self.create_inference_features(player_data)
            
            for stat, model_path in self.model_paths.items():
                if Path(model_path).exists():
                    pred_df = predict_with_compatible_model(model_path, input_df)
                    if pred_df is not None:
                        prediction = pred_df.iloc[0]['prediction']
                        player_result[f'pred_{stat}'] = prediction
                        player_result[f'error_{stat}'] = abs(prediction - player_data[stat])
                        player_result[f'error_pct_{stat}'] = abs(prediction - player_data[stat]) / max(player_data[stat], 1.0) * 100
                    else:
                        player_result[f'pred_{stat}'] = 0.0
                        player_result[f'error_{stat}'] = player_data[stat]
                        player_result[f'error_pct_{stat}'] = 100.0
                else:
                    logger.warning(f"Model not found: {model_path}")
            
            results.append(player_result)
        
        return pd.DataFrame(results)
    
    def analyze_prediction_patterns(self, results_df: pd.DataFrame):
        """Analyze prediction patterns and biases"""
        
        logger.info("🔍 Analyzing prediction patterns...")
        
        # Create comprehensive analysis
        fig, axes = plt.subplots(3, 2, figsize=(15, 18))
        fig.suptitle('Model Error Analysis - 7/5/25 Validation Games', fontsize=16, fontweight='bold')
        
        stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
        
        for i, stat in enumerate(stats):
            row = i // 2
            col = i % 2
            ax = axes[row, col]
            
            # Get actual vs predicted
            actual = results_df[f'actual_{stat}']
            predicted = results_df[f'pred_{stat}']
            errors = results_df[f'error_{stat}']
            
            # Scatter plot: Actual vs Predicted
            ax.scatter(actual, predicted, alpha=0.7, s=60)
            
            # Perfect prediction line
            min_val = min(actual.min(), predicted.min())
            max_val = max(actual.max(), predicted.max())
            ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='Perfect Prediction')
            
            # Calculate metrics
            mae = errors.mean()
            rmse = np.sqrt((errors ** 2).mean())
            within_1 = (errors <= 1.0).mean() * 100
            within_2 = (errors <= 2.0).mean() * 100
            
            # Add player labels for high errors
            for idx, row_data in results_df.iterrows():
                if row_data[f'error_{stat}'] > mae * 1.5:  # High error threshold
                    ax.annotate(row_data['player'].split()[-1], 
                              (row_data[f'actual_{stat}'], row_data[f'pred_{stat}']),
                              xytext=(5, 5), textcoords='offset points', fontsize=8, alpha=0.7)
            
            ax.set_xlabel(f'Actual {stat.title()}')
            ax.set_ylabel(f'Predicted {stat.title()}')
            ax.set_title(f'{stat.title()}: MAE={mae:.2f}, RMSE={rmse:.2f}\nWithin 1.0: {within_1:.1f}%, Within 2.0: {within_2:.1f}%')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('model_error_analysis.png', dpi=300, bbox_inches='tight')
        logger.info("📊 Error analysis plot saved as 'model_error_analysis.png'")
        
        # Print detailed analysis
        self._print_detailed_analysis(results_df)
    
    def _print_detailed_analysis(self, results_df: pd.DataFrame):
        """Print detailed error analysis"""
        
        logger.info("\n" + "="*80)
        logger.info("📊 DETAILED ERROR ANALYSIS")
        logger.info("="*80)
        
        stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
        
        for stat in stats:
            actual = results_df[f'actual_{stat}']
            predicted = results_df[f'pred_{stat}']
            errors = results_df[f'error_{stat}']
            
            mae = errors.mean()
            rmse = np.sqrt((errors ** 2).mean())
            within_1 = (errors <= 1.0).mean() * 100
            within_2 = (errors <= 2.0).mean() * 100
            
            # Bias analysis
            bias = (predicted - actual).mean()
            
            logger.info(f"\n🎯 {stat.upper()} ANALYSIS:")
            logger.info(f"   MAE: {mae:.3f}")
            logger.info(f"   RMSE: {rmse:.3f}")
            logger.info(f"   Bias: {bias:.3f} ({'over-prediction' if bias > 0 else 'under-prediction'})")
            logger.info(f"   Within 1.0: {within_1:.1f}%")
            logger.info(f"   Within 2.0: {within_2:.1f}%")
            logger.info(f"   Actual range: [{actual.min():.1f}, {actual.max():.1f}]")
            logger.info(f"   Predicted range: [{predicted.min():.1f}, {predicted.max():.1f}]")
            
            # Identify worst predictions
            worst_errors = results_df.nlargest(3, f'error_{stat}')
            logger.info(f"   Worst predictions:")
            for _, row in worst_errors.iterrows():
                logger.info(f"     {row['player']:20s}: actual={row[f'actual_{stat}']:4.1f}, pred={row[f'pred_{stat}']:4.1f}, error={row[f'error_{stat}']:4.1f}")
    
    def analyze_feature_correlation_issues(self):
        """Analyze potential feature correlation issues"""
        
        logger.info("\n🔍 ANALYZING FEATURE CORRELATION ISSUES")
        logger.info("="*60)
        
        # Create feature correlation matrix for a sample player
        sample_player = self.validation_data.iloc[0].to_dict()
        features_df = self.create_inference_features(sample_player)
        
        logger.info("📊 Current 13-feature inference setup:")
        for i, feature in enumerate(features_df.columns):
            logger.info(f"   {i+1:2d}. {feature:20s}: {features_df.iloc[0][feature]:8.3f}")
        
        # Identify potential issues
        logger.info("\n⚠️ IDENTIFIED ISSUES:")
        logger.info("1. FEATURE DIMENSION MISMATCH:")
        logger.info("   - Models trained with ~50 features from real WNBA data")
        logger.info("   - Inference using only 13 basic features")
        logger.info("   - Scaler expects 13 features but models expect 50")
        
        logger.info("\n2. MISSING CRITICAL FEATURES:")
        logger.info("   - No usage rate or pace factors")
        logger.info("   - No rolling averages or recent form")
        logger.info("   - No player archetype or position-specific features")
        logger.info("   - No opponent-specific defensive metrics")
        
        logger.info("\n3. STATIC FEATURE VALUES:")
        logger.info("   - Many features use default/static values")
        logger.info("   - Limited player-specific differentiation")
        logger.info("   - No real contextual adaptation")
        
        logger.info("\n4. REGRESSION-TO-MEAN BEHAVIOR:")
        logger.info("   - Points model severely under-predicts stars (Kelsey Mitchell: 28→5.7)")
        logger.info("   - Models predict similar values for different player types")
        logger.info("   - Lack of feature diversity causes conservative predictions")
    
    def create_improvement_recommendations(self):
        """Create specific improvement recommendations"""
        
        logger.info("\n🎯 IMPROVEMENT RECOMMENDATIONS")
        logger.info("="*60)
        
        logger.info("1. IMMEDIATE FIXES:")
        logger.info("   ✅ Use enhanced_player_props_pipeline.py for comprehensive features")
        logger.info("   ✅ Retrain models with 50+ features matching training data")
        logger.info("   ✅ Implement proper feature scaling alignment")
        logger.info("   ✅ Add player-specific contextual features")
        
        logger.info("\n2. FEATURE ENGINEERING IMPROVEMENTS:")
        logger.info("   📊 Add usage rate and pace factors")
        logger.info("   📊 Include rolling averages (last 5 games)")
        logger.info("   📊 Add player archetype classifications")
        logger.info("   📊 Include opponent defensive ratings")
        logger.info("   📊 Add game context (home/away, rest, etc.)")
        
        logger.info("\n3. MODEL ARCHITECTURE IMPROVEMENTS:")
        logger.info("   🧠 Use Huber loss for robustness to outliers")
        logger.info("   🧠 Implement stat-specific model architectures")
        logger.info("   🧠 Add ensemble methods for better predictions")
        logger.info("   🧠 Include uncertainty quantification")
        
        logger.info("\n4. TRAINING DATA IMPROVEMENTS:")
        logger.info("   📈 Use more diverse training examples")
        logger.info("   📈 Include outlier performances in training")
        logger.info("   📈 Add data augmentation for rare events")
        logger.info("   📈 Balance training data across player tiers")

def main():
    """Main analysis function"""
    
    logger.info("📊 MODEL ERROR ANALYSIS - 7/5/25 VALIDATION GAMES")
    logger.info("="*80)
    
    # Create analyzer
    analyzer = ModelErrorAnalyzer()
    
    # Collect all predictions
    results_df = analyzer.collect_all_predictions()
    
    # Analyze patterns
    analyzer.analyze_prediction_patterns(results_df)
    
    # Analyze feature issues
    analyzer.analyze_feature_correlation_issues()
    
    # Create recommendations
    analyzer.create_improvement_recommendations()
    
    logger.info("\n✅ Error analysis completed!")
    logger.info("📊 Check 'model_error_analysis.png' for visualizations")

if __name__ == "__main__":
    main()
