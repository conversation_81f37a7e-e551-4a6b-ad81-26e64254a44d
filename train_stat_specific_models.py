#!/usr/bin/env python3
"""
🎯 STAT-SPECIFIC NEURAL MODEL TRAINING
Enhanced training with stat-specific optimizations for non-points models
"""

import asyncio
import logging
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.neural_cortex.enhanced_player_props_pipeline import EnhancedPlayerPropsTrainingPipeline, create_enhanced_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def train_stat_specific_model(prop_type: str) -> dict:
    """Train a single stat-specific model with optimized configurations"""
    
    logger.info(f"🎯 Training STAT-SPECIFIC {prop_type.upper()} model...")
    logger.info("-" * 50)
    
    try:
        # Special handling for points to restore original performance
        if prop_type == "points":
            logger.info("🔧 POINTS: Using original successful configuration (MSE loss, 300 epochs)")
            config = create_enhanced_config(
                league="WNBA",
                prop_type=prop_type,
                num_epochs=300,  # More training for points
                learning_rate=0.001,
                hidden_dim=256,
                num_layers=4,
                early_stopping_patience=15,
                model_save_path=f"models/{prop_type}_stat_specific_fixed.pt"
            )
        else:
            # Create stat-specific enhanced configuration for other stats
            config = create_enhanced_config(
                league="WNBA",
                prop_type=prop_type,
                model_save_path=f"models/{prop_type}_stat_specific.pt"
            )
        
        # Initialize enhanced trainer with explicit prop_type
        trainer = EnhancedPlayerPropsTrainingPipeline(config, prop_type=prop_type)
        
        # Train model
        results = await trainer.train()
        
        logger.info(f"✅ {prop_type.upper()} model completed!")
        logger.info(f"   Test MAE: {results['test_mae']}")
        logger.info(f"   Test R²: {results['test_r2']}")
        
        return {
            'prop_type': prop_type,
            'test_mae': results['test_mae'],
            'test_r2': results['test_r2'],
            'model_path': config.model_save_path
        }
        
    except Exception as e:
        logger.error(f"❌ Error training {prop_type} model: {str(e)}")
        return {
            'prop_type': prop_type,
            'error': str(e)
        }

async def main():
    """Train all stat-specific models with enhanced configurations"""
    
    logger.info("🚀 STARTING STAT-SPECIFIC MODEL TRAINING")
    logger.info("=" * 60)
    logger.info("🎯 Focus: Improving non-points models with stat-specific optimizations")
    logger.info("📊 Enhancements:")
    logger.info("   • Stat-specific loss functions (MAE for sparse stats)")
    logger.info("   • Optimized model architectures per stat type")
    logger.info("   • Enhanced feature engineering for each stat")
    logger.info("   • Stat-specific hyperparameter tuning")
    logger.info("   • Increased data augmentation for sparse stats")
    logger.info("=" * 60)
    
    # Define models to train (focus on problematic non-points stats)
    models_to_train = [
        "rebounds",   # R²=-0.20 → Target: R²>0.1
        "assists",    # R²=0.06 → Target: R²>0.2  
        "steals",     # R²=0.11 → Target: R²>0.2
        "blocks",     # R²=-0.01 → Target: R²>0.1
        "threes",     # R²=-0.08 → Target: R²>0.1
        "points"      # R²=0.36 → Maintain performance
    ]
    
    results = []
    
    # Train each model
    for prop_type in models_to_train:
        result = await train_stat_specific_model(prop_type)
        results.append(result)
        logger.info("")
    
    # Summary
    logger.info("=" * 60)
    logger.info("🎯 STAT-SPECIFIC TRAINING SUMMARY")
    logger.info("=" * 60)
    
    for result in results:
        if 'error' in result:
            logger.info(f"❌ {result['prop_type'].upper()}: ERROR - {result['error']}")
        else:
            mae = result['test_mae']
            r2 = result['test_r2']
            
            # Determine improvement status
            if result['prop_type'] == 'points':
                status = "✅" if r2 > 0.3 else "⚠️"
            elif result['prop_type'] in ['rebounds', 'blocks', 'threes']:
                status = "✅" if r2 > 0.1 else "⚠️"
            else:  # assists, steals
                status = "✅" if r2 > 0.2 else "⚠️"
                
            logger.info(f"{status} {result['prop_type'].upper()}: MAE={mae:.4f}, R²={r2:.4f}")
    
    logger.info("")
    logger.info(f"🎯 Successfully trained {len([r for r in results if 'error' not in r])}/{len(results)} models")
    
    # Check for improvements
    improved_models = []
    baseline_r2 = {
        'points': 0.364, 'rebounds': -0.20, 'assists': 0.06,
        'steals': 0.11, 'blocks': -0.01, 'threes': -0.08
    }
    
    for result in results:
        if 'error' not in result:
            prop_type = result['prop_type']
            new_r2 = result['test_r2']
            old_r2 = baseline_r2.get(prop_type, 0)
            
            if new_r2 > old_r2:
                improvement = new_r2 - old_r2
                improved_models.append(f"{prop_type} (+{improvement:.3f})")
    
    if improved_models:
        logger.info(f"📈 IMPROVEMENTS: {', '.join(improved_models)}")
    else:
        logger.info("📊 No significant improvements detected - may need further tuning")

if __name__ == "__main__":
    asyncio.run(main())
