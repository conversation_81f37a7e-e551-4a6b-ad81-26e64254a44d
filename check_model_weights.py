#!/usr/bin/env python3
"""
Check model weights and training status
"""

import torch
import torch.nn as nn
import numpy as np
from sklearn.preprocessing import RobustScaler

print("🔍 MODEL WEIGHTS ANALYSIS")
print("="*50)

class SimpleEnhancedModel(nn.Module):
    def __init__(self, input_dim=54, hidden_dims=[256, 256, 128], dropout_rate=0.3, use_batch_norm=True):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            if dropout_rate > 0:
                layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim
        
        self.main_network = nn.Sequential(*layers)
        self.calibration = nn.Linear(prev_dim, 1)
    
    def forward(self, x):
        x = self.main_network(x)
        x = self.calibration(x)
        return x

def analyze_model_weights(model_path, stat_name):
    print(f"\n🎯 {stat_name.upper()} MODEL ANALYSIS")
    print("-" * 30)
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location='cpu')
    model_config = checkpoint['model_config']
    
    # Create and load model
    model = SimpleEnhancedModel(**model_config)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Check final layer weights (calibration layer)
    final_layer = model.calibration
    weight = final_layer.weight.data.numpy()
    bias = final_layer.bias.data.numpy()
    
    print(f"Final layer weight shape: {weight.shape}")
    print(f"Final layer weight range: [{weight.min():.6f}, {weight.max():.6f}]")
    print(f"Final layer weight mean: {weight.mean():.6f}")
    print(f"Final layer bias: {bias[0]:.6f}")
    
    # Check if weights are initialized (close to zero) or trained
    weight_magnitude = np.abs(weight).mean()
    if weight_magnitude < 0.01:
        print("⚠️ Weights appear to be near initialization values (not trained)")
    else:
        print("✅ Weights appear to be trained")
    
    # Test with different inputs
    print(f"\nTesting predictions:")
    
    # Test with zero input
    zero_input = torch.zeros(1, 54)
    with torch.no_grad():
        zero_pred = model(zero_input).item()
    print(f"Zero input → {zero_pred:.3f}")
    
    # Test with ones input
    ones_input = torch.ones(1, 54)
    with torch.no_grad():
        ones_pred = model(ones_input).item()
    print(f"Ones input → {ones_pred:.3f}")
    
    # Test with realistic basketball stats (first 6 features)
    if stat_name.lower() == 'points':
        realistic_stats = [20.0, 5.0, 3.0, 1.0, 0.5, 2.0]  # points, reb, ast, stl, blk, 3pt
    else:
        realistic_stats = [15.0, 8.0, 2.0, 1.0, 1.0, 1.0]  # points, reb, ast, stl, blk, 3pt
    
    realistic_input = torch.tensor([realistic_stats + [1.0] * (54-6)], dtype=torch.float32)
    with torch.no_grad():
        realistic_pred = model(realistic_input).item()
    print(f"Realistic input → {realistic_pred:.3f}")
    
    # Test with scaled input using saved scaler
    scaler_params = checkpoint['feature_scaler_params']
    if 'center_' in scaler_params:
        scaler = RobustScaler()
        scaler.center_ = np.array(scaler_params['center_'])
        scaler.scale_ = np.array(scaler_params['scale_'])
        scaler.n_features_in_ = 54
        
        realistic_array = np.array([realistic_stats + [1.0] * (54-6)])
        realistic_scaled = scaler.transform(realistic_array)
        realistic_scaled_tensor = torch.tensor(realistic_scaled, dtype=torch.float32)
        
        with torch.no_grad():
            scaled_pred = model(realistic_scaled_tensor).item()
        print(f"Scaled realistic input → {scaled_pred:.3f}")
        
        # Show scaling effect
        print(f"Scaling multiplier: {scaled_pred / realistic_pred:.3f}x")
        
        # Check scaled input range
        print(f"Scaled input range: [{realistic_scaled.min():.3f}, {realistic_scaled.max():.3f}]")
    
    return model, checkpoint

# Analyze both models
models = [
    ('models/points_enhanced_model.pt', 'Points'),
    ('models/rebounds_enhanced_model.pt', 'Rebounds')
]

for model_path, stat_name in models:
    try:
        model, checkpoint = analyze_model_weights(model_path, stat_name)
    except Exception as e:
        print(f"❌ Error analyzing {stat_name}: {e}")

print(f"\n🎯 SUMMARY:")
print("Check if models are properly trained or just initialized")
print("Look for extreme predictions that indicate scaling issues")
