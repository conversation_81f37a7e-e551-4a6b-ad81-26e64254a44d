#!/usr/bin/env python3
"""
🔍 QUICK TEST FIXED MODELS
=========================

Quick test of the retrained enhanced models
"""

import torch
import numpy as np
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def quick_test_model(model_path: str, prop_type: str):
    """Quick test of a model"""
    
    logger.info(f"\n🔍 Testing {prop_type} model: {model_path}")
    
    try:
        # Load model
        checkpoint = torch.load(model_path, map_location='cpu')

        # Reconstruct model from config and state dict
        from enhanced_player_props_pipeline import EnhancedPlayerPropsModel
        model_config = checkpoint['model_config']
        model = EnhancedPlayerPropsModel(**model_config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Get scaler parameters
        scaler_params = checkpoint['feature_scaler_params']
        feature_list = checkpoint['feature_list']
        
        logger.info(f"📊 Features: {len(feature_list)}")
        logger.info(f"📊 Scaler mean range: [{min(scaler_params['mean_']):.3f}, {max(scaler_params['mean_']):.3f}]")
        logger.info(f"📊 Scaler scale range: [{min(scaler_params['scale_']):.3f}, {max(scaler_params['scale_']):.3f}]")
        
        # Create test input (54 features, all zeros)
        test_input = np.zeros(54, dtype=np.float32)
        
        # Apply scaling
        mean_array = np.array(scaler_params['mean_'])
        scale_array = np.array(scaler_params['scale_'])
        test_input_scaled = (test_input - mean_array) / scale_array
        
        # Make prediction
        with torch.no_grad():
            test_tensor = torch.FloatTensor(test_input_scaled).unsqueeze(0)
            prediction = model(test_tensor).item()
        
        # Check if realistic
        realistic_ranges = {
            'points': (5, 35),
            'rebounds': (1, 15),
            'assists': (1, 12),
            'steals': (0, 5),
            'blocks': (0, 4),
            'threes': (0, 8)
        }
        
        expected_min, expected_max = realistic_ranges.get(prop_type, (0, 50))
        is_realistic = expected_min <= prediction <= expected_max
        
        logger.info(f"🎯 Prediction: {prediction:.2f}")
        logger.info(f"📊 Expected range: [{expected_min}, {expected_max}]")
        logger.info(f"✅ Realistic: {'YES' if is_realistic else 'NO'}")
        
        return {
            'prop_type': prop_type,
            'prediction': prediction,
            'is_realistic': is_realistic,
            'expected_range': (expected_min, expected_max)
        }
        
    except Exception as e:
        logger.error(f"❌ Error testing {prop_type}: {e}")
        return None

def main():
    """Test fixed models"""
    
    logger.info("🔍 QUICK TEST FIXED ENHANCED MODELS")
    logger.info("="*40)
    
    models = [
        ('points', 'models/points_enhanced_model_fixed.pt'),
        ('rebounds', 'models/rebounds_enhanced_model_fixed.pt')
    ]
    
    results = []
    
    for prop_type, model_path in models:
        result = quick_test_model(model_path, prop_type)
        if result:
            results.append(result)
    
    # Summary
    logger.info(f"\n🎯 SUMMARY:")
    logger.info("="*40)
    
    realistic_count = sum(1 for r in results if r['is_realistic'])
    total_count = len(results)
    
    for result in results:
        status = "✅" if result['is_realistic'] else "❌"
        logger.info(f"{result['prop_type'].upper()}: {result['prediction']:.2f} {status}")
    
    logger.info(f"\n📊 {realistic_count}/{total_count} models realistic")
    
    if realistic_count == total_count:
        logger.info("🎉 SUCCESS: Fixed models are realistic!")
    else:
        logger.info("⚠️ Some models still need work")

if __name__ == "__main__":
    main()
