#!/usr/bin/env python3
"""
Test the model config
"""

import torch

def main():
    print("🔍 Checking model config...")
    
    try:
        checkpoint = torch.load('models/points_enhanced_model_FIXED_v2.pt', map_location='cpu')
        print(f"Keys in checkpoint: {list(checkpoint.keys())}")
        
        if 'model_config' in checkpoint:
            config = checkpoint['model_config']
            print(f"Model config found: {config}")
        else:
            print("❌ No model_config found in checkpoint")
            
        # Check the actual model state dict structure
        state_dict = checkpoint['model_state_dict']
        print(f"\nModel layers:")
        for key in sorted(state_dict.keys()):
            if 'weight' in key:
                shape = state_dict[key].shape
                print(f"  {key}: {shape}")
                
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
