#!/usr/bin/env python3
"""
Quick test of the fixed points model
"""

import torch
import numpy as np
from enhanced_player_props_pipeline import EnhancedPlayerPropsModel

def main():
    print("🔍 Quick test of fixed points model...")
    
    # Load model
    checkpoint = torch.load('models/points_enhanced_model_FIXED_v2.pt', map_location='cpu')
    
    # Use the saved model config
    model_config = checkpoint['model_config']
    print(f"Model config: {model_config}")
    
    # Create model with saved config
    model = EnhancedPlayerPropsModel(**model_config)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Get scaler params
    scaler_params = checkpoint['feature_scaler_params']
    mean_array = np.array(scaler_params['mean_'])
    scale_array = np.array(scaler_params['scale_'])
    
    # Test with realistic features
    test_features = np.random.randn(3, 54) * 0.5  # More realistic scale
    
    # Scale features
    test_features_scaled = (test_features - mean_array) / scale_array
    
    # Make predictions
    with torch.no_grad():
        test_tensor = torch.FloatTensor(test_features_scaled)
        predictions = model(test_tensor).squeeze()
    
    print(f"🎯 Test predictions: {predictions.tolist()}")
    print(f"📊 Prediction range: [{predictions.min():.2f}, {predictions.max():.2f}]")
    
    # Check if realistic
    if predictions.min() >= 3 and predictions.max() <= 35:
        print("✅ FIXED! Model produces realistic predictions!")
        print("🎯 Ready for validation against actual game data")
    else:
        print("⚠️ Still needs adjustment")
    
    return predictions.min().item(), predictions.max().item()

if __name__ == "__main__":
    min_pred, max_pred = main()
    print(f"\nSUMMARY: Prediction range [{min_pred:.2f}, {max_pred:.2f}]")
