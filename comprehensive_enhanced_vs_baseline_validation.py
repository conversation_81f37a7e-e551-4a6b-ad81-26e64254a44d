#!/usr/bin/env python3
"""
Comprehensive Enhanced vs True Baseline Model Validation
Tests enhanced model (40 features) against true baseline model (30 features)
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
import torch
import numpy as np
from datetime import datetime

async def test_true_baseline_model(service, player_data, prop_type='points'):
    """Force use of true baseline model (30 features) by temporarily modifying service"""

    # Save original enhanced model path
    original_enhanced_path = "models/enhanced_basketball_models/best_points_model.pt"
    temp_enhanced_path = "models/enhanced_basketball_models/best_points_model_temp.pt"

    try:
        # Temporarily rename enhanced model to force baseline usage
        if os.path.exists(original_enhanced_path):
            os.rename(original_enhanced_path, temp_enhanced_path)

        # Create game data for unified prediction
        game_data = {
            'home_team': 'LAS',
            'away_team': 'IND',
            'league': 'WNBA'
        }

        # Now predict using true baseline model
        result = await service.predict_unified(game_data, [player_data])

        if result and result.player_props:
            # Get first player's prediction
            first_player_key = list(result.player_props.keys())[0]
            prediction = result.player_props[first_player_key].get(prop_type, 0)
            return prediction
        else:
            return 0.0

    finally:
        # Restore enhanced model
        if os.path.exists(temp_enhanced_path):
            os.rename(temp_enhanced_path, original_enhanced_path)

async def analyze_model_checkpoints():
    """Analyze both enhanced and baseline model checkpoints"""
    
    print("\n🔍 MODEL CHECKPOINT ANALYSIS")
    print("=" * 60)
    
    # Enhanced model analysis
    enhanced_path = "models/enhanced_basketball_models/best_points_model.pt"
    baseline_path = "models/real_basketball_models/best_points_model.pt"
    
    models_info = {}
    
    for model_name, path in [("Enhanced", enhanced_path), ("Baseline", baseline_path)]:
        if os.path.exists(path):
            checkpoint = torch.load(path, map_location='cpu')
            
            feature_list = checkpoint.get('feature_list', [])
            model_config = checkpoint.get('model_config', {})
            training_info = checkpoint.get('training_info', {})
            
            models_info[model_name] = {
                'features': len(feature_list),
                'feature_list': feature_list,
                'hidden_dim': model_config.get('hidden_dim', 'Unknown'),
                'validation_loss': training_info.get('best_val_loss', 'Unknown'),
                'epoch': training_info.get('best_epoch', 'Unknown')
            }
            
            print(f"\n📊 {model_name} Model:")
            print(f"  Features: {len(feature_list)}")
            print(f"  Hidden Dim: {model_config.get('hidden_dim', 'Unknown')}")
            print(f"  Best Validation Loss: {training_info.get('best_val_loss', 'Unknown')}")
            print(f"  Best Epoch: {training_info.get('best_epoch', 'Unknown')}")
            
            # Show enhanced features if present
            enhanced_features = ['usage_rate', 'field_goal_attempts', 'three_point_attempts', 
                               'free_throw_attempts', 'recent_points_avg_5', 'recent_points_avg_10',
                               'team_pace', 'opponent_def_rating', 'home_game', 'starter_status']
            
            enhanced_present = [f for f in enhanced_features if f in feature_list]
            if enhanced_present:
                print(f"  Enhanced Features Present: {len(enhanced_present)}")
                for feature in enhanced_present:
                    print(f"    ✅ {feature}")
        else:
            print(f"\n❌ {model_name} Model not found at {path}")
    
    return models_info

async def run_comprehensive_validation():
    """Run comprehensive validation against actual boxscore data"""
    
    print("\n🎯 COMPREHENSIVE VALIDATION AGAINST ACTUAL BOXSCORE DATA")
    print("=" * 60)
    
    service = UnifiedNeuralPredictionService()
    
    # Recent WNBA boxscore data for validation
    validation_players = [
        {
            'name': "A'ja Wilson",
            'team': 'LAS',
            'actual_points': 21.0,
            'data': {
                'name': "A'ja Wilson",
                'team': 'LAS',
                'position': 'F',
                'minutes_per_game': 34.2,
                'points_per_game': 22.8,
                'rebounds_per_game': 9.4,
                'assists_per_game': 2.3,
                'steals_per_game': 1.8,
                'blocks_per_game': 2.2,
                'turnovers_per_game': 2.8,
                'field_goals_made_per_game': 8.9,
                'field_goals_attempted_per_game': 16.2,
                'three_pointers_made_per_game': 0.4,
                'three_pointers_attempted_per_game': 1.2,
                'free_throws_made_per_game': 4.6,
                'free_throws_attempted_per_game': 5.8,
                'games_played': 38,
                'starter': True,
                'tier': 'Elite'
            }
        },
        {
            'name': "Kelsey Plum",
            'team': 'LAS',
            'actual_points': 17.0,
            'data': {
                'name': "Kelsey Plum",
                'team': 'LAS',
                'position': 'G',
                'minutes_per_game': 32.1,
                'points_per_game': 17.8,
                'rebounds_per_game': 2.6,
                'assists_per_game': 4.2,
                'steals_per_game': 1.1,
                'blocks_per_game': 0.2,
                'turnovers_per_game': 2.4,
                'field_goals_made_per_game': 6.8,
                'field_goals_attempted_per_game': 15.1,
                'three_pointers_made_per_game': 2.8,
                'three_pointers_attempted_per_game': 7.2,
                'free_throws_made_per_game': 1.4,
                'free_throws_attempted_per_game': 1.7,
                'games_played': 40,
                'starter': True,
                'tier': 'Star'
            }
        },
        {
            'name': "Chelsea Gray",
            'team': 'LAS',
            'actual_points': 12.0,
            'data': {
                'name': "Chelsea Gray",
                'team': 'LAS',
                'position': 'G',
                'minutes_per_game': 29.8,
                'points_per_game': 12.3,
                'rebounds_per_game': 3.1,
                'assists_per_game': 6.8,
                'steals_per_game': 1.2,
                'blocks_per_game': 0.1,
                'turnovers_per_game': 2.9,
                'field_goals_made_per_game': 4.7,
                'field_goals_attempted_per_game': 10.2,
                'three_pointers_made_per_game': 1.8,
                'three_pointers_attempted_per_game': 4.1,
                'free_throws_made_per_game': 1.1,
                'free_throws_attempted_per_game': 1.3,
                'games_played': 36,
                'starter': True,
                'tier': 'Solid'
            }
        }
    ]
    
    enhanced_errors = []
    baseline_errors = []
    
    print(f"\n{'Player':<15} {'Actual':<8} {'Enhanced':<10} {'Baseline':<10} {'Enh Error':<10} {'Base Error':<10}")
    print("-" * 75)
    
    for player in validation_players:
        # Create game data for unified prediction
        game_data = {
            'home_team': 'LAS',
            'away_team': 'IND',
            'league': 'WNBA'
        }

        # Enhanced model prediction
        enhanced_result = await service.predict_unified(game_data, [player['data']])
        if enhanced_result and enhanced_result.player_props:
            first_player_key = list(enhanced_result.player_props.keys())[0]
            enhanced_pred = enhanced_result.player_props[first_player_key].get('points', 0)
        else:
            enhanced_pred = 0.0

        # True baseline model prediction
        baseline_pred = await test_true_baseline_model(service, player['data'], 'points')

        # Calculate errors
        enhanced_error = abs(enhanced_pred - player['actual_points'])
        baseline_error = abs(baseline_pred - player['actual_points'])

        enhanced_errors.append(enhanced_error)
        baseline_errors.append(baseline_error)

        print(f"{player['name']:<15} {player['actual_points']:<8.1f} {enhanced_pred:<10.2f} {baseline_pred:<10.2f} {enhanced_error:<10.2f} {baseline_error:<10.2f}")
    
    # Calculate summary statistics
    enhanced_mae = np.mean(enhanced_errors)
    baseline_mae = np.mean(baseline_errors)
    improvement = ((baseline_mae - enhanced_mae) / baseline_mae) * 100
    
    print("\n📊 VALIDATION SUMMARY")
    print("-" * 40)
    print(f"Enhanced Model MAE: {enhanced_mae:.2f} points")
    print(f"Baseline Model MAE: {baseline_mae:.2f} points")
    print(f"Improvement: {improvement:.1f}%")
    
    if improvement > 0:
        print(f"✅ Enhanced model performs {improvement:.1f}% better than baseline!")
    else:
        print(f"❌ Enhanced model performs {abs(improvement):.1f}% worse than baseline")
    
    return enhanced_mae, baseline_mae, improvement

async def main():
    print("🔍 COMPREHENSIVE ENHANCED vs TRUE BASELINE MODEL COMPARISON")
    print("=" * 70)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Analyze model checkpoints
    models_info = await analyze_model_checkpoints()
    
    # Step 2: Run comprehensive validation
    enhanced_mae, baseline_mae, improvement = await run_comprehensive_validation()
    
    # Step 3: Feature importance analysis (if enhanced model exists)
    if 'Enhanced' in models_info:
        print("\n🎯 FEATURE IMPORTANCE ANALYSIS")
        print("=" * 50)
        
        enhanced_features = models_info['Enhanced']['feature_list']
        baseline_features = models_info.get('Baseline', {}).get('feature_list', [])
        
        # Identify new features in enhanced model
        new_features = [f for f in enhanced_features if f not in baseline_features]
        
        print(f"New Features in Enhanced Model ({len(new_features)}):")
        for feature in new_features:
            print(f"  ✨ {feature}")
        
        print(f"\nShared Features ({len([f for f in enhanced_features if f in baseline_features])}):")
        shared_features = [f for f in enhanced_features if f in baseline_features]
        for feature in shared_features[:10]:  # Show first 10
            print(f"  🔄 {feature}")
        if len(shared_features) > 10:
            print(f"  ... and {len(shared_features) - 10} more")
    
    # Step 4: Final assessment
    print("\n🏆 FINAL ASSESSMENT")
    print("=" * 40)
    
    if improvement > 5:
        print("✅ ENHANCED MODEL IS SIGNIFICANTLY BETTER")
        print(f"   - {improvement:.1f}% improvement over baseline")
        print("   - Ready for production deployment")
    elif improvement > 0:
        print("✅ ENHANCED MODEL IS BETTER")
        print(f"   - {improvement:.1f}% improvement over baseline")
        print("   - Modest but meaningful improvement")
    else:
        print("❌ ENHANCED MODEL NEEDS IMPROVEMENT")
        print(f"   - {abs(improvement):.1f}% worse than baseline")
        print("   - Enhanced features may not be effective")
    
    print(f"\nModel Comparison:")
    if 'Enhanced' in models_info and 'Baseline' in models_info:
        print(f"  Enhanced: {models_info['Enhanced']['features']} features, {models_info['Enhanced']['hidden_dim']} hidden")
        print(f"  Baseline: {models_info['Baseline']['features']} features, {models_info['Baseline']['hidden_dim']} hidden")
        print(f"  Training Loss: Enhanced {models_info['Enhanced']['validation_loss']} vs Baseline {models_info['Baseline']['validation_loss']}")

if __name__ == "__main__":
    asyncio.run(main())
