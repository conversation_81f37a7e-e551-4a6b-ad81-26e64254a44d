#!/usr/bin/env python3
"""
Final test of the fixed points model with correct architecture
"""

import torch
import numpy as np
from enhanced_player_props_pipeline import EnhancedPlayerPropsModel

def main():
    print("🔍 FINAL TEST OF FIXED POINTS MODEL")
    print("="*50)
    
    try:
        # Load checkpoint
        checkpoint = torch.load('models/points_enhanced_model_FIXED_v2.pt', map_location='cpu')
        
        # Create model with the ACTUAL architecture used in training
        # Based on the error messages, the model has [512, 256, 128, 64] architecture
        model = EnhancedPlayerPropsModel(
            input_dim=54,
            hidden_dims=[512, 256, 128, 64],  # The actual architecture
            dropout_rate=0.2,
            use_batch_norm=True
        )
        
        # Load the state dict
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print("✅ Model loaded successfully with correct architecture!")
        
        # Get scaler parameters
        scaler_params = checkpoint['feature_scaler_params']
        mean_array = np.array(scaler_params['mean_'])
        scale_array = np.array(scaler_params['scale_'])
        
        # Test with realistic features for different player types
        test_cases = [
            ("Star Player", np.random.randn(54) * 0.5 + 1.0),  # Above average
            ("Role Player", np.random.randn(54) * 0.3),        # Average
            ("Bench Player", np.random.randn(54) * 0.3 - 0.5)  # Below average
        ]
        
        predictions = []
        for name, features in test_cases:
            # Scale features
            features_scaled = (features - mean_array) / scale_array
            
            # Make prediction
            with torch.no_grad():
                features_tensor = torch.FloatTensor(features_scaled).unsqueeze(0)
                prediction = model(features_tensor).squeeze().item()
            
            predictions.append(prediction)
            print(f"🏀 {name}: {prediction:.1f} points")
        
        # Check if predictions are realistic
        min_pred, max_pred = min(predictions), max(predictions)
        print(f"\n📊 Prediction range: [{min_pred:.1f}, {max_pred:.1f}]")
        
        if 3 <= min_pred and max_pred <= 35:
            print("✅ EXCELLENT! Model produces realistic predictions!")
            print("🎯 Points model is FIXED and ready for validation!")
            
            # Test with more samples to verify consistency
            print("\n🔍 Testing with 10 random samples...")
            all_preds = []
            for i in range(10):
                test_features = np.random.randn(54) * 0.4
                test_features_scaled = (test_features - mean_array) / scale_array
                
                with torch.no_grad():
                    test_tensor = torch.FloatTensor(test_features_scaled).unsqueeze(0)
                    pred = model(test_tensor).squeeze().item()
                    all_preds.append(pred)
            
            print(f"📊 10 sample predictions: {[f'{p:.1f}' for p in all_preds]}")
            print(f"📊 Range: [{min(all_preds):.1f}, {max(all_preds):.1f}]")
            
            if min(all_preds) >= 1 and max(all_preds) <= 40:
                print("✅ CONSISTENT REALISTIC PREDICTIONS!")
                print("🚀 Ready to proceed with validation against 7/5/25 games!")
            else:
                print("⚠️ Some predictions still unrealistic")
                
        else:
            print("⚠️ Predictions still need adjustment")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
