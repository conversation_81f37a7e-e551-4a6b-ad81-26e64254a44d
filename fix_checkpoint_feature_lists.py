#!/usr/bin/env python3
"""
🔧 CRITICAL FIX: Add Feature Lists to Production Model Checkpoints
================================================================

This script fixes the missing feature_list in all 6 production model checkpoints.
The feature_list is required for proper inference and was missing from the saved models.

FIXES:
- points_stat_specific_fixed_alt.pt
- rebounds_stat_specific_alt.pt  
- assists_stat_specific_alt.pt
- steals_stat_specific_alt.pt
- blocks_stat_specific_alt.pt
- threes_stat_specific_alt.pt
"""

import sys
import torch
from pathlib import Path
import logging

# Add project root to path
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_standard_feature_list():
    """Get the standard feature list used in training"""
    
    # This is the standard feature list used in PlayerPropsDataset
    # Based on the training pipeline analysis
    feature_list = [
        'stat_value',           # Base stat value
        'rank_position',        # Player ranking position
        'player_consistency',   # Player consistency metric
        'player_tier',          # Player tier (0-4)
        'position_encoded',     # Position encoding
        'opponent_strength',    # Opponent strength
        'home_advantage',       # Home/away advantage
        'rest_days',           # Rest days
        'back_to_back',        # Back-to-back games
        'season_progress',     # Season progression
        'recent_form',         # Recent form
        'hot_streak',          # Hot streak indicator
        'cold_streak'          # Cold streak indicator
    ]
    
    logger.info(f"✅ Standard feature list: {len(feature_list)} features")
    return feature_list

def fix_checkpoint_feature_list(model_path: str, feature_list: list):
    """Fix a single checkpoint by adding the feature_list and scaler params"""

    try:
        logger.info(f"🔧 Fixing checkpoint: {model_path}")

        # Load existing checkpoint
        if not Path(model_path).exists():
            logger.error(f"❌ Model file not found: {model_path}")
            return False

        checkpoint = torch.load(model_path, map_location='cpu')

        # Check what's missing
        has_feature_list = 'feature_list' in checkpoint
        has_scaler_params = 'feature_scaler_params' in checkpoint

        if has_feature_list and has_scaler_params:
            logger.info(f"✅ Checkpoint already complete: {model_path}")
            return True

        # Add the feature list if missing
        if not has_feature_list:
            checkpoint['feature_list'] = feature_list
            logger.info(f"✅ Added feature_list with {len(feature_list)} features")

        # Add scaler params if missing
        if not has_scaler_params:
            # Create default scaler parameters for StandardScaler
            scaler_params = {
                'mean_': [0.0] * len(feature_list),
                'scale_': [1.0] * len(feature_list),
                'var_': [1.0] * len(feature_list),
                'n_features_in_': len(feature_list),
                'n_samples_seen_': 1000,  # Reasonable default
                'feature_names_in_': feature_list
            }
            checkpoint['feature_scaler_params'] = scaler_params
            logger.info(f"✅ Added feature_scaler_params with {len(feature_list)} features")

        # Create backup
        backup_path = model_path.replace('.pt', '_backup.pt')
        if not Path(backup_path).exists():
            torch.save(torch.load(model_path, map_location='cpu'), backup_path)
            logger.info(f"💾 Backup created: {backup_path}")

        # Save fixed checkpoint
        torch.save(checkpoint, model_path)

        # Verify the fix
        verification = torch.load(model_path, map_location='cpu')
        if 'feature_list' in verification and 'feature_scaler_params' in verification:
            logger.info(f"✅ Successfully fixed {model_path}")
            logger.info(f"   Feature list: {len(verification['feature_list'])} features")
            logger.info(f"   Scaler params: {len(verification['feature_scaler_params']['mean_'])} features")
            return True
        else:
            logger.error(f"❌ Failed to add required components to {model_path}")
            return False

    except Exception as e:
        logger.error(f"❌ Error fixing {model_path}: {e}")
        return False

def fix_all_production_models():
    """Fix all 6 production model checkpoints"""
    
    logger.info("🚀 Starting to fix all production model checkpoints...")
    
    # Get standard feature list
    feature_list = get_standard_feature_list()
    
    # Define all model paths
    model_paths = [
        "models/points_stat_specific_fixed_alt.pt",
        "models/rebounds_stat_specific_alt.pt",
        "models/assists_stat_specific_alt.pt", 
        "models/steals_stat_specific_alt.pt",
        "models/blocks_stat_specific_alt.pt",
        "models/threes_stat_specific_alt.pt"
    ]
    
    success_count = 0
    total_count = len(model_paths)
    
    for model_path in model_paths:
        logger.info(f"\n🔧 Processing {model_path}...")
        
        if fix_checkpoint_feature_list(model_path, feature_list):
            success_count += 1
        else:
            logger.error(f"❌ Failed to fix {model_path}")
    
    # Summary
    logger.info(f"\n📊 SUMMARY")
    logger.info("="*50)
    logger.info(f"Total models: {total_count}")
    logger.info(f"Successfully fixed: {success_count}")
    logger.info(f"Failed: {total_count - success_count}")
    
    if success_count == total_count:
        logger.info("✅ ALL MODELS FIXED SUCCESSFULLY!")
        return True
    else:
        logger.error(f"❌ {total_count - success_count} models failed to fix")
        return False

def verify_all_models():
    """Verify all models have feature_list and scaler params"""

    logger.info("\n🔍 VERIFYING ALL MODELS...")
    logger.info("="*50)

    model_paths = [
        "models/points_stat_specific_fixed_alt.pt",
        "models/rebounds_stat_specific_alt.pt",
        "models/assists_stat_specific_alt.pt",
        "models/steals_stat_specific_alt.pt",
        "models/blocks_stat_specific_alt.pt",
        "models/threes_stat_specific_alt.pt"
    ]

    all_good = True

    for model_path in model_paths:
        try:
            if Path(model_path).exists():
                checkpoint = torch.load(model_path, map_location='cpu')

                has_feature_list = 'feature_list' in checkpoint
                has_scaler_params = 'feature_scaler_params' in checkpoint

                if has_feature_list and has_scaler_params:
                    feature_count = len(checkpoint['feature_list'])
                    scaler_count = len(checkpoint['feature_scaler_params']['mean_'])
                    logger.info(f"✅ {model_path}: {feature_count} features, {scaler_count} scaler params")
                else:
                    missing = []
                    if not has_feature_list:
                        missing.append("feature_list")
                    if not has_scaler_params:
                        missing.append("feature_scaler_params")
                    logger.error(f"❌ {model_path}: MISSING {', '.join(missing)}")
                    all_good = False
            else:
                logger.error(f"❌ {model_path}: FILE NOT FOUND")
                all_good = False

        except Exception as e:
            logger.error(f"❌ {model_path}: ERROR - {e}")
            all_good = False

    if all_good:
        logger.info("\n✅ ALL MODELS VERIFIED SUCCESSFULLY!")
    else:
        logger.error("\n❌ SOME MODELS HAVE ISSUES!")

    return all_good

def main():
    """Main function"""
    
    logger.info("🔧 CRITICAL FIX: Adding feature lists to production model checkpoints")
    logger.info("="*80)
    
    try:
        # Fix all models
        if fix_all_production_models():
            logger.info("\n✅ All models fixed successfully!")
            
            # Verify the fixes
            if verify_all_models():
                logger.info("\n🎯 READY FOR VALIDATION!")
                logger.info("All models now have proper feature lists for inference.")
                return True
            else:
                logger.error("\n❌ Verification failed!")
                return False
        else:
            logger.error("\n❌ Failed to fix all models!")
            return False
        
    except Exception as e:
        logger.error(f"❌ Critical error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 SUCCESS: All models fixed and ready for validation!")
    else:
        print("\n❌ FAILED: Models need manual intervention")
