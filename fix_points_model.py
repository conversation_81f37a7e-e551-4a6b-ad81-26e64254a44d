#!/usr/bin/env python3
"""
Fix Points Model - Restore R²=0.364 Performance
Retrain only the points model with corrected configuration
"""

import asyncio
import logging
import os
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.neural_cortex.enhanced_player_props_pipeline import EnhancedPlayerPropsTrainingPipeline, create_enhanced_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def fix_points_model():
    """Fix the points model to restore original R²=0.364 performance"""
    
    logger.info("🔧 FIXING POINTS MODEL - Restoring R²=0.364 Performance")
    logger.info("=" * 60)
    
    # Create enhanced config for points with corrected settings
    config = create_enhanced_config(
        league="WNBA",
        prop_type="points",
        # Restore original successful configuration
        num_epochs=300,  # Increased from 150 to ensure full training
        learning_rate=0.001,  # Standard rate that worked
        hidden_dim=256,  # Keep larger network for points complexity
        num_layers=4,  # Keep deeper network
        use_batch_norm=False,  # Keep disabled for small datasets
        early_stopping_patience=15,  # Allow more patience
        # MSE loss will be set automatically in the pipeline
    )

    logger.info(f"🎯 Training POINTS model with corrected configuration:")
    logger.info(f"   Epochs: {config.num_epochs}")
    logger.info(f"   Loss: MSE (original successful)")
    logger.info(f"   Hidden Dim: {config.hidden_dim}")
    logger.info(f"   Layers: {config.num_layers}")
    logger.info(f"   Learning Rate: {config.learning_rate}")

    # Initialize enhanced trainer with explicit prop_type
    trainer = EnhancedPlayerPropsTrainingPipeline(config, prop_type="points")
    
    # Train model
    results = await trainer.train()
    
    logger.info("✅ POINTS model retraining completed!")
    logger.info(f"   Test MAE: {results['test_mae']}")
    logger.info(f"   Test R²: {results['test_r2']}")
    
    # Check if we restored performance
    if results['test_r2'] > 0.3:
        logger.info("🎉 SUCCESS! Points model performance restored!")
    else:
        logger.warning("⚠️ Points model still underperforming. May need further adjustments.")
    
    return results

if __name__ == "__main__":
    asyncio.run(fix_points_model())
