#!/usr/bin/env python3
"""
Check if the enhanced models have target scaling
"""

import torch
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_target_scaling(model_path):
    """Check if model has target scaling parameters"""
    try:
        logger.info(f"🔍 Checking target scaling for: {model_path}")
        checkpoint = torch.load(model_path, map_location='cpu')
        
        logger.info(f"📋 Checkpoint keys: {list(checkpoint.keys())}")
        
        if 'target_scaler_params' in checkpoint:
            target_scaler = checkpoint['target_scaler_params']
            logger.info(f"✅ Target scaler found!")
            logger.info(f"   Keys: {list(target_scaler.keys())}")
            
            if 'mean_' in target_scaler:
                logger.info(f"   Mean: {target_scaler['mean_']}")
            if 'scale_' in target_scaler:
                logger.info(f"   Scale: {target_scaler['scale_']}")
            if 'center_' in target_scaler:
                logger.info(f"   Center: {target_scaler['center_']}")
                
        else:
            logger.info("❌ No target_scaler_params found")
            
        if 'training_results' in checkpoint:
            results = checkpoint['training_results']
            logger.info(f"📊 Training results: {results}")
            
        return checkpoint
        
    except Exception as e:
        logger.error(f"❌ Error checking model: {e}")
        return None

if __name__ == "__main__":
    # Check the working model
    check_target_scaling("models/points_enhanced_model_fixed.pt")
    print("\n" + "="*60 + "\n")
    check_target_scaling("models/rebounds_enhanced_model_fixed.pt")
