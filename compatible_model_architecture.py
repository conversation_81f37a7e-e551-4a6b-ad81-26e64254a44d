#!/usr/bin/env python3
"""
🔧 COMPATIBLE MODEL ARCHITECTURE
===============================

This module provides a model architecture that matches the saved checkpoints
which have 'main_network' and 'calibration' layers instead of just 'network'.
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompatiblePlayerPropsNeuralNetwork(nn.Module):
    """Neural network that matches the saved checkpoint architecture"""

    def __init__(self, input_dim: int, hidden_dim: int = 128, num_layers: int = 3,
                 dropout_rate: float = 0.3, output_activation: str = "linear",
                 use_batch_norm: bool = False, use_calibration_layer: bool = True):
        super(CompatiblePlayerPropsNeuralNetwork, self).__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.dropout_rate = dropout_rate
        self.output_activation = output_activation
        self.use_batch_norm = use_batch_norm
        self.use_calibration_layer = use_calibration_layer

        # Build main network layers - EXACTLY matching saved architecture
        layers = []

        # Input layer
        layers.append(nn.Linear(input_dim, hidden_dim))
        if use_batch_norm:
            layers.append(nn.BatchNorm1d(hidden_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Dropout(dropout_rate))

        # Hidden layers
        for i in range(num_layers - 1):
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))

        # Final layer of main network - outputs to 1 dimension
        layers.append(nn.Linear(hidden_dim, 1))

        # Output activation for main network
        if output_activation == "sigmoid":
            layers.append(nn.Sigmoid())
        elif output_activation == "relu":
            layers.append(nn.ReLU())

        self.main_network = nn.Sequential(*layers)

        # Calibration layer - takes 1 input, outputs 1
        if use_calibration_layer:
            self.calibration = nn.Linear(1, 1)

        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize network weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        """Forward pass"""
        # Main network (outputs 1 dimension)
        main_output = self.main_network(x)

        # Calibration layer (1 -> 1 transformation)
        if self.use_calibration_layer:
            output = self.calibration(main_output)
        else:
            output = main_output

        return output
    
    def forward_inference(self, x):
        """Forward pass for inference (handles batch norm for single samples)"""
        if x.size(0) == 1 and self.use_batch_norm:
            # For single sample inference, use eval mode
            self.eval()
            with torch.no_grad():
                return self.forward(x)
        else:
            return self.forward(x)

def load_compatible_model(checkpoint_path: str, device: str = 'cpu'):
    """Load a model using the compatible architecture"""
    
    try:
        logger.info(f"🔧 Loading compatible model: {checkpoint_path}")
        
        # Load checkpoint
        checkpoint = torch.load(checkpoint_path, map_location=device)
        config = checkpoint['config']
        
        # Determine actual input dimension from saved weights
        model_state = checkpoint['model_state_dict']
        actual_input_dim = model_state['main_network.0.weight'].shape[1]

        logger.info(f"📊 Detected input dimension: {actual_input_dim}")

        # Create model with compatible architecture
        model = CompatiblePlayerPropsNeuralNetwork(
            input_dim=actual_input_dim,  # Use actual dimension from saved weights
            hidden_dim=config.get('hidden_dim', 128),
            num_layers=config.get('num_layers', 3),
            dropout_rate=config.get('dropout_rate', 0.3),
            output_activation=config.get('output_activation', 'linear'),
            use_batch_norm=config.get('use_batch_norm', False),
            use_calibration_layer=config.get('use_calibration_layer', True)
        )
        
        # Load state dict
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        if device != 'cpu':
            model.to(device)
        
        logger.info(f"✅ Successfully loaded compatible model")
        return model, checkpoint
        
    except Exception as e:
        logger.error(f"❌ Error loading compatible model: {e}")
        return None, None

def predict_with_compatible_model(checkpoint_path: str, input_df: pd.DataFrame, device: str = 'cpu'):
    """Make prediction using compatible model architecture"""
    
    try:
        # Load model and checkpoint
        model, checkpoint = load_compatible_model(checkpoint_path, device)
        if model is None:
            return None
        
        # Get feature list and scaler params
        feature_list = checkpoint.get('feature_list', None)
        if feature_list is None:
            logger.error("No feature_list found in checkpoint")
            return None
        
        scaler_params = checkpoint.get('feature_scaler_params', None)
        if scaler_params is None:
            logger.error("No feature_scaler_params found in checkpoint")
            return None
        
        # Get actual input dimension from model
        model_state = checkpoint['model_state_dict']
        actual_input_dim = model_state['main_network.0.weight'].shape[1]

        # Align features to match actual input dimension
        aligned_df = align_features_for_inference(input_df, feature_list, actual_input_dim)

        # Handle scaler dimension mismatch
        from sklearn.preprocessing import StandardScaler
        scaler_n_features = scaler_params.get('n_features_in_', 13)

        if scaler_n_features != actual_input_dim:
            logger.warning(f"⚠️ Scaler dimension mismatch: scaler={scaler_n_features}, model={actual_input_dim}")

            # Use only the base features for scaling, pad the rest with zeros
            base_features = aligned_df.iloc[:, :scaler_n_features]
            extra_features = aligned_df.iloc[:, scaler_n_features:]

            # Scale base features
            feature_scaler = StandardScaler()
            for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                if scaler_params.get(attr) is not None:
                    setattr(feature_scaler, attr, scaler_params[attr])

            X_base = feature_scaler.transform(base_features.values)
            X_extra = extra_features.values  # Don't scale extra features
            X = np.concatenate([X_base, X_extra], axis=1)
        else:
            # Normal scaling
            feature_scaler = StandardScaler()
            for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                if scaler_params.get(attr) is not None:
                    setattr(feature_scaler, attr, scaler_params[attr])
            X = feature_scaler.transform(aligned_df.values)
        
        # Predict
        with torch.no_grad():
            X_tensor = torch.tensor(X, dtype=torch.float32)
            if device != 'cpu':
                X_tensor = X_tensor.to(device)
            outputs = model.forward_inference(X_tensor).cpu().numpy().flatten()
        
        # Unscale predictions if target scaler exists
        target_scaler_params = checkpoint.get('target_scaler_params', None)
        if target_scaler_params is not None:
            target_scaler = StandardScaler()
            for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                if target_scaler_params.get(attr) is not None:
                    setattr(target_scaler, attr, target_scaler_params[attr])
            predictions = target_scaler.inverse_transform(outputs.reshape(-1, 1)).flatten()
        else:
            predictions = outputs
        
        # Create result DataFrame
        result_df = input_df.copy()
        result_df['prediction'] = predictions
        
        return result_df
        
    except Exception as e:
        logger.error(f"❌ Prediction failed: {e}")
        return None

def align_features_for_inference(input_df: pd.DataFrame, feature_list: list, target_dim: int) -> pd.DataFrame:
    """Align input features to match training feature list and target dimension"""

    aligned_df = pd.DataFrame()

    # First, add features from feature_list
    for feature in feature_list:
        if feature in input_df.columns:
            aligned_df[feature] = input_df[feature]
        else:
            # Fill missing features with default values
            if 'stat_value' in feature:
                aligned_df[feature] = 0.0
            elif 'rank' in feature:
                aligned_df[feature] = 50.0
            elif 'consistency' in feature:
                aligned_df[feature] = 0.7
            elif 'tier' in feature:
                aligned_df[feature] = 2.0
            elif 'position' in feature:
                aligned_df[feature] = 1.0
            elif 'opponent' in feature:
                aligned_df[feature] = 0.5
            elif 'home' in feature:
                aligned_df[feature] = 0.5
            elif 'rest' in feature:
                aligned_df[feature] = 1.0
            elif 'back_to_back' in feature:
                aligned_df[feature] = 0.0
            elif 'season' in feature:
                aligned_df[feature] = 0.5
            elif 'form' in feature:
                aligned_df[feature] = 0.0
            elif 'streak' in feature:
                aligned_df[feature] = 0.0
            else:
                aligned_df[feature] = 0.0

    # Pad with zeros if we need more features to match target_dim
    current_dim = len(aligned_df.columns)
    if current_dim < target_dim:
        for i in range(current_dim, target_dim):
            aligned_df[f'padding_feature_{i}'] = 0.0

    # Truncate if we have too many features
    elif current_dim > target_dim:
        aligned_df = aligned_df.iloc[:, :target_dim]

    logger.info(f"📊 Aligned features: {len(aligned_df.columns)} -> {target_dim}")

    return aligned_df

def test_compatible_model():
    """Test the compatible model with a sample prediction"""
    
    logger.info("🧪 Testing compatible model...")
    
    # Create sample input
    sample_data = {
        'stat_value': [15.0],
        'rank_position': [25.0],
        'player_consistency': [0.8],
        'player_tier': [1.0],
        'position_encoded': [2.0],
        'opponent_strength': [0.6],
        'home_advantage': [1.0],
        'rest_days': [1.0],
        'back_to_back': [0.0],
        'season_progress': [0.7],
        'recent_form': [16.0],
        'hot_streak': [1.0],
        'cold_streak': [0.0]
    }
    
    input_df = pd.DataFrame(sample_data)
    
    # Test with points model
    model_path = "models/points_stat_specific_fixed_alt.pt"
    if Path(model_path).exists():
        result_df = predict_with_compatible_model(model_path, input_df)
        if result_df is not None:
            prediction = result_df.iloc[0]['prediction']
            logger.info(f"✅ Test prediction: {prediction:.2f}")
            return True
        else:
            logger.error("❌ Test prediction failed")
            return False
    else:
        logger.error(f"❌ Model file not found: {model_path}")
        return False

if __name__ == "__main__":
    test_compatible_model()
