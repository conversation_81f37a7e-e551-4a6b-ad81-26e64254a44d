#!/usr/bin/env python3
"""
Test regression-to-mean fixes with enhanced monitoring
"""

import asyncio
import logging
from src.neural_cortex.enhanced_player_props_pipeline import EnhancedPlayerPropsConfig, EnhancedPlayerPropsTrainingPipeline

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_regression_fixes():
    """Test training with enhanced monitoring and fixes"""
    
    logger.info("🎯 Testing Regression-to-Mean Fixes")
    logger.info("=" * 60)
    
    # Configuration with conservative settings
    config = EnhancedPlayerPropsConfig(
        prop_type="points",
        league="WNBA",
        
        # Conservative training settings
        num_epochs=50,  # More epochs to see learning
        batch_size=32,
        learning_rate=0.001,  # Conservative learning rate
        early_stopping_patience=15,
        
        # Simpler model architecture
        hidden_dim=128,  # Smaller, simpler model
        num_layers=2,  # Fewer layers
        dropout_rate=0.2,  # Less dropout
        
        # Enhanced features
        add_interaction_features=True,
        feature_selection_threshold=0.03,
        max_features=40,  # Fewer features
        
        # Loss function
        loss_function="mse",  # Start with simple MSE
        
        # No ensemble for testing
        ensemble_size=1,
        
        # Minimal data augmentation
        noise_augmentation=False,  # Disable for testing
        
        # Model save
        model_save_path="regression_test_points.pt"  # Save in root directory
    )
    
    logger.info(f"📋 Test Configuration:")
    logger.info(f"   Epochs: {config.num_epochs}")
    logger.info(f"   Learning Rate: {config.learning_rate}")
    logger.info(f"   Model: {config.hidden_dim}x{config.num_layers}")
    logger.info(f"   Features: {config.max_features} max")
    logger.info(f"   Loss: {config.loss_function}")
    logger.info(f"   Batch Size: {config.batch_size}")
    
    # Create pipeline
    pipeline = EnhancedPlayerPropsTrainingPipeline(config)
    
    # Override target scaling to prevent regression-to-mean
    def no_target_scaling(self):
        """Override to disable target scaling"""
        logger.info("🎯 Target scaling DISABLED - using raw target values")
        return None, None
    
    # Monkey patch the target scaling method
    pipeline._setup_target_scaling = lambda: no_target_scaling(pipeline)
    
    logger.info("🚀 Starting regression fix test...")
    
    try:
        result = await pipeline.train()
        
        if result:
            logger.info("✅ Training completed!")
            logger.info(f"📊 Final validation MAE: {result.get('val_mae', 'N/A')}")
            logger.info(f"📊 Final test MAE: {result.get('test_mae', 'N/A')}")
            
            # Analyze final predictions
            if 'val_predictions' in result and 'val_actuals' in result:
                import numpy as np
                preds = np.array(result['val_predictions'])
                actuals = np.array(result['val_actuals'])
                
                logger.info("📊 FINAL PREDICTION ANALYSIS:")
                logger.info(f"   Predictions - Range: {preds.min():.2f} to {preds.max():.2f}")
                logger.info(f"   Predictions - Mean: {preds.mean():.2f}, Std: {preds.std():.2f}")
                logger.info(f"   Actuals - Range: {actuals.min():.2f} to {actuals.max():.2f}")
                logger.info(f"   Actuals - Mean: {actuals.mean():.2f}, Std: {actuals.std():.2f}")
                
                pred_spread = preds.max() - preds.min()
                actual_spread = actuals.max() - actuals.min()
                spread_ratio = pred_spread / actual_spread if actual_spread > 0 else 0
                
                logger.info(f"   Spread Ratio: {spread_ratio:.3f}")
                
                if spread_ratio > 0.3:
                    logger.info("✅ Good spread ratio - model is learning diversity!")
                elif spread_ratio > 0.1:
                    logger.info("⚠️ Moderate spread ratio - some learning but could improve")
                else:
                    logger.info("❌ Low spread ratio - still showing regression-to-mean")
                    
        else:
            logger.error("❌ Training returned no results")
            
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_regression_fixes())
