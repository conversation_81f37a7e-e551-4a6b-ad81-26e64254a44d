#!/usr/bin/env python3
"""
Diagnostic script to test Enhanced vs Baseline model performance
and identify why they're producing identical results.
"""

import asyncio
import logging
import sys
import os
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test data with enhanced features
TEST_PLAYERS = [
    {
        'name': 'A\'ja <PERSON>',
        'points': 14.8,
        'rebounds': 8.1,
        'assists': 1.9,
        'steals': 0.8,
        'blocks': 1.2,
        'threes': 1.8,
        'games_played': 32,
        'minutes_per_game': 28.0,
        'field_goal_percentage': 0.456,
        'free_throw_percentage': 0.798,
        'age': 28,
        'usage_rate': 22.1,
        'field_goal_attempts': 11.2,
        'three_point_attempts': 4.5,
        'free_throw_attempts': 3.8,
        'recent_points_5': 16.4,
        'recent_points_10': 15.2,
        'team_pace': 98.2,
        'opponent_def_rating': 108.5,
        'home_game': 1.0,
        'actual_points': 21  # From boxscore
    },
    {
        'name': '<PERSON> Plum',
        'points': 18.8,
        'rebounds': 3.2,
        'assists': 4.8,
        'steals': 1.4,
        'blocks': 0.2,
        'threes': 2.9,
        'games_played': 38,
        'minutes_per_game': 35.0,
        'field_goal_percentage': 0.442,
        'free_throw_percentage': 0.856,
        'age': 30,
        'usage_rate': 26.8,
        'field_goal_attempts': 14.5,
        'three_point_attempts': 7.2,
        'free_throw_attempts': 4.1,
        'recent_points_5': 20.2,
        'recent_points_10': 19.4,
        'team_pace': 98.2,
        'opponent_def_rating': 108.5,
        'home_game': 1.0,
        'actual_points': 20  # From boxscore
    }
]

async def test_enhanced_model_detection():
    """Test if enhanced model is being detected and used correctly"""
    
    logger.info("🔬 ENHANCED MODEL DETECTION TEST")
    logger.info("=" * 60)
    
    service = UnifiedNeuralPredictionService()
    
    for player in TEST_PLAYERS:
        logger.info(f"\n🏀 Testing {player['name']}")
        
        # Test 1: Full enhanced features (should trigger enhanced model)
        logger.info("   📊 Test 1: Full Enhanced Features")
        enhanced_data = player.copy()
        
        # Create game data for unified prediction
        game_data = {
            'home_team': 'LAS',
            'away_team': 'IND',
            'league': 'WNBA'
        }

        result_enhanced = await service.predict_unified(game_data, [enhanced_data])

        if result_enhanced and result_enhanced.player_props:
            # Get first player's prediction
            first_player_key = list(result_enhanced.player_props.keys())[0]
            pred_enhanced = result_enhanced.player_props[first_player_key].get('points', 0)
            logger.info(f"      Enhanced Model Prediction: {pred_enhanced:.2f} points")
        else:
            logger.error("      ❌ Enhanced prediction failed")
            continue
            
        # Test 2: Minimal features (should trigger baseline model)
        logger.info("   📊 Test 2: Minimal Features (Baseline)")
        baseline_data = {
            'name': player['name'],
            'points': player['points'],
            'rebounds': player['rebounds'],
            'assists': player['assists'],
            'steals': player['steals'],
            'blocks': player['blocks'],
            'threes': player['threes'],
            'games_played': player['games_played'],
            'minutes_per_game': player['minutes_per_game'],
            'field_goal_percentage': player['field_goal_percentage'],
            'free_throw_percentage': player['free_throw_percentage'],
            'age': player.get('age', 26),
            # NO enhanced features
        }
        
        result_baseline = await service.predict_unified(game_data, [baseline_data])

        if result_baseline and result_baseline.player_props:
            # Get first player's prediction
            first_player_key = list(result_baseline.player_props.keys())[0]
            pred_baseline = result_baseline.player_props[first_player_key].get('points', 0)
            logger.info(f"      Baseline Model Prediction: {pred_baseline:.2f} points")
        else:
            logger.error("      ❌ Baseline prediction failed")
            continue
            
        # Compare predictions
        diff = abs(pred_enhanced - pred_baseline)
        actual = player['actual_points']
        
        logger.info(f"   📈 Results:")
        logger.info(f"      Enhanced: {pred_enhanced:.2f} (Error: {abs(pred_enhanced - actual):.2f})")
        logger.info(f"      Baseline: {pred_baseline:.2f} (Error: {abs(pred_baseline - actual):.2f})")
        logger.info(f"      Difference: {diff:.2f} points")
        
        if diff < 0.1:
            logger.warning(f"      🚨 IDENTICAL PREDICTIONS - Enhanced model not working!")
        else:
            logger.info(f"      ✅ Different predictions - Enhanced model working")

async def test_feature_creation():
    """Test if enhanced features are being created correctly"""
    
    logger.info("\n🔧 FEATURE CREATION TEST")
    logger.info("=" * 60)
    
    # Import the service to access internal methods
    service = UnifiedNeuralPredictionService()
    
    # Test enhanced feature creation
    test_player = TEST_PLAYERS[0]
    
    # Load enhanced model to get feature list
    import torch
    checkpoint = torch.load('models/enhanced_basketball_models/best_points_model.pt', map_location='cpu')
    feature_list = checkpoint.get('feature_list', [])
    
    logger.info(f"📋 Enhanced model expects {len(feature_list)} features:")
    for i, feature in enumerate(feature_list[:10]):
        logger.info(f"   {i+1:2d}. {feature}")
    if len(feature_list) > 10:
        logger.info(f"   ... and {len(feature_list) - 10} more")
    
    # Create enhanced features
    try:
        enhanced_features = service._create_enhanced_points_features(test_player, feature_list)
        logger.info(f"\n🎯 Enhanced features created: {len(enhanced_features)} features")
        logger.info(f"   Sample values: {enhanced_features[:10]}")
        
        # Check for enhanced-specific features
        enhanced_indices = []
        for i, feature_name in enumerate(feature_list):
            if feature_name in ['usage_rate', 'field_goal_attempts', 'recent_points_avg_5']:
                enhanced_indices.append((i, feature_name, enhanced_features[i]))
        
        logger.info(f"\n🔍 Enhanced-specific features:")
        for idx, name, value in enhanced_indices:
            logger.info(f"   {idx:2d}. {name}: {value:.3f}")
            
    except Exception as e:
        logger.error(f"❌ Enhanced feature creation failed: {e}")

async def main():
    """Main diagnostic function"""
    
    logger.info("🚀 ENHANCED vs BASELINE MODEL DIAGNOSTIC")
    logger.info("=" * 80)
    
    # Test 1: Model detection and usage
    await test_enhanced_model_detection()
    
    # Test 2: Feature creation
    await test_feature_creation()
    
    logger.info("\n🎉 DIAGNOSTIC COMPLETE")
    logger.info("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
