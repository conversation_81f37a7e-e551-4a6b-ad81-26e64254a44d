#!/usr/bin/env python3
"""
🔧 FIX POINTS MODEL TRAINING
===========================

Fix the points model under-prediction issue with targeted training improvements
"""

import sys
import os
sys.path.append('.')

import torch
import torch.nn as nn
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from enhanced_player_props_pipeline import EnhancedPlayerPropsModel, EnhancedTrainingPipeline, create_validation_data_for_training
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FixedPointsTrainingPipeline(EnhancedTrainingPipeline):
    """Enhanced training pipeline with fixes for points model"""
    
    def train_enhanced_model(self, X: np.ndarray, y: np.ndarray, feature_names: list) -> dict:
        """Train enhanced model with points-specific fixes"""
        
        logger.info(f"🔧 Training FIXED enhanced {self.prop_type} model")
        logger.info(f"📊 Training data: mean={np.mean(y):.2f}, range=[{np.min(y):.1f}, {np.max(y):.1f}]")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Use StandardScaler
        self.feature_scaler = StandardScaler()
        X_train_scaled = self.feature_scaler.fit_transform(X_train)
        X_test_scaled = self.feature_scaler.transform(X_test)
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train).to(self.device)
        X_test_tensor = torch.FloatTensor(X_test_scaled).to(self.device)
        y_test_tensor = torch.FloatTensor(y_test).to(self.device)
        
        # Create model with points-specific architecture
        self.model = EnhancedPlayerPropsModel(
            input_dim=X.shape[1],
            hidden_dims=[512, 256, 128, 64],  # Deeper network for points
            dropout_rate=0.2,  # Lower dropout for points
            use_batch_norm=True
        ).to(self.device)
        
        # Points-specific training setup
        optimizer = torch.optim.AdamW(
            self.model.parameters(), 
            lr=0.002,  # Higher learning rate for points
            weight_decay=0.005  # Lower weight decay
        )
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, patience=15, factor=0.7
        )
        
        # Use MSE loss for points (better for regression)
        criterion = nn.MSELoss()
        
        # Training loop with points-specific parameters
        best_loss = float('inf')
        patience_counter = 0
        training_history = {'train_loss': [], 'val_loss': [], 'val_mae': []}
        
        logger.info("🚀 Starting training with points-specific optimizations...")
        
        for epoch in range(300):  # More epochs for points
            # Training
            self.model.train()
            optimizer.zero_grad()
            
            train_pred = self.model(X_train_tensor).squeeze()
            train_loss = criterion(train_pred, y_train_tensor)
            
            train_loss.backward()
            
            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # Validation
            self.model.eval()
            with torch.no_grad():
                val_pred = self.model(X_test_tensor).squeeze()
                val_loss = criterion(val_pred, y_test_tensor)
                val_mae = torch.mean(torch.abs(val_pred - y_test_tensor))
            
            # Learning rate scheduling
            scheduler.step(val_loss)
            
            # Track history
            training_history['train_loss'].append(train_loss.item())
            training_history['val_loss'].append(val_loss.item())
            training_history['val_mae'].append(val_mae.item())
            
            # Early stopping with more patience for points
            if val_loss < best_loss:
                best_loss = val_loss
                patience_counter = 0
                best_model_state = self.model.state_dict().copy()
            else:
                patience_counter += 1
                if patience_counter >= 30:  # More patience for points
                    logger.info(f"Early stopping at epoch {epoch}")
                    break
            
            if epoch % 25 == 0:
                logger.info(f"Epoch {epoch}: train_loss={train_loss:.4f}, val_loss={val_loss:.4f}, val_mae={val_mae:.4f}")
                
                # Log sample predictions for debugging
                with torch.no_grad():
                    sample_pred = val_pred[:3]
                    sample_actual = y_test_tensor[:3]
                    logger.info(f"   Sample predictions: {sample_pred.tolist()}")
                    logger.info(f"   Sample actuals: {sample_actual.tolist()}")
        
        # Load best model
        self.model.load_state_dict(best_model_state)
        
        # Final evaluation
        self.model.eval()
        with torch.no_grad():
            final_pred = self.model(X_test_tensor).squeeze()
            final_mae = torch.mean(torch.abs(final_pred - y_test_tensor)).item()
            final_mse = torch.mean((final_pred - y_test_tensor) ** 2).item()
            
            # Calculate R²
            y_mean = torch.mean(y_test_tensor)
            ss_tot = torch.sum((y_test_tensor - y_mean) ** 2)
            ss_res = torch.sum((y_test_tensor - final_pred) ** 2)
            r2 = 1 - (ss_res / ss_tot)
            
            # Log final predictions for debugging
            logger.info(f"📊 Final predictions range: [{final_pred.min():.2f}, {final_pred.max():.2f}]")
            logger.info(f"📊 Actual range: [{y_test_tensor.min():.2f}, {y_test_tensor.max():.2f}]")
        
        results = {
            'final_mae': final_mae,
            'final_mse': final_mse,
            'r2_score': r2.item(),
            'training_history': training_history,
            'feature_names': feature_names,
            'model_state': best_model_state,
            'model_config': {
                'input_dim': X.shape[1],
                'hidden_dims': [512, 256, 128, 64],
                'dropout_rate': 0.2,
                'use_batch_norm': True
            }
        }
        
        logger.info(f"✅ FIXED training completed - MAE: {final_mae:.3f}, R²: {r2:.3f}")
        
        return results

def main():
    """Train fixed points model"""
    
    logger.info("🔧 TRAINING FIXED POINTS MODEL")
    logger.info("="*50)
    
    try:
        # Create validation data
        validation_df = create_validation_data_for_training()
        
        # Create fixed pipeline
        pipeline = FixedPointsTrainingPipeline('points')
        
        # Create enhanced training data
        X, y, feature_names = pipeline.create_enhanced_training_data(validation_df)
        
        # Train with fixes
        results = pipeline.train_enhanced_model(X, y, feature_names)
        
        # Save fixed model
        save_path = "models/points_enhanced_model_FIXED_v2.pt"
        pipeline.save_enhanced_model(results, save_path)
        
        logger.info(f"✅ Fixed points model saved to: {save_path}")
        logger.info(f"📊 Final performance: MAE={results['final_mae']:.3f}, R²={results['r2_score']:.3f}")
        
        # Quick test
        logger.info("\n🔍 Quick test of fixed model...")
        
        # Load and test
        checkpoint = torch.load(save_path, map_location='cpu')
        model_config = checkpoint['model_config']
        model = EnhancedPlayerPropsModel(**model_config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Test with realistic features
        test_features = np.random.randn(3, 54) * 0.5  # More realistic test features
        scaler_params = checkpoint['feature_scaler_params']
        mean_array = np.array(scaler_params['mean_'])
        scale_array = np.array(scaler_params['scale_'])
        
        test_features_scaled = (test_features - mean_array) / scale_array
        
        with torch.no_grad():
            test_tensor = torch.FloatTensor(test_features_scaled)
            predictions = model(test_tensor).squeeze()
            
        logger.info(f"🎯 Test predictions: {predictions.tolist()}")
        logger.info(f"📊 Prediction range: [{predictions.min():.2f}, {predictions.max():.2f}]")
        
        if predictions.min() >= 3 and predictions.max() <= 35:
            logger.info("✅ Fixed model produces realistic predictions!")
        else:
            logger.info("⚠️ Model still needs adjustment")
        
    except Exception as e:
        logger.error(f"❌ Error in fixed training: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
