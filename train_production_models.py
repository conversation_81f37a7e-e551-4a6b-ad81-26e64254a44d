#!/usr/bin/env python3
"""
Production Model Training Script
Train all neural models with regression-to-mean fixes
"""

import asyncio
import logging
from src.neural_cortex.enhanced_player_props_pipeline import EnhancedPlayerPropsTrainingPipeline, EnhancedPlayerPropsConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def train_production_models():
    """Train all production models with optimized settings"""
    
    logger.info("🚀 PRODUCTION MODEL TRAINING")
    logger.info("=" * 60)
    
    # Model configurations for different props
    models_to_train = [
        {
            'prop_type': 'points',
            'model_name': 'enhanced_points_production',
            'epochs': 100,
            'hidden_dim': 256,
            'num_layers': 4
        },
        {
            'prop_type': 'rebounds',
            'model_name': 'enhanced_rebounds_production', 
            'epochs': 100,
            'hidden_dim': 256,
            'num_layers': 4
        },
        {
            'prop_type': 'assists',
            'model_name': 'enhanced_assists_production',
            'epochs': 100,
            'hidden_dim': 256,
            'num_layers': 4
        },
        {
            'prop_type': 'steals',
            'model_name': 'enhanced_steals_production',
            'epochs': 80,
            'hidden_dim': 128,
            'num_layers': 3
        },
        {
            'prop_type': 'blocks',
            'model_name': 'enhanced_blocks_production',
            'epochs': 80,
            'hidden_dim': 128,
            'num_layers': 3
        },
        {
            'prop_type': 'threes',
            'model_name': 'enhanced_threes_production',
            'epochs': 80,
            'hidden_dim': 128,
            'num_layers': 3
        }
    ]
    
    results = {}
    
    for model_config in models_to_train:
        logger.info(f"\n🎯 Training {model_config['prop_type'].upper()} model...")
        logger.info("-" * 50)
        
        try:
            # Create enhanced configuration
            config = EnhancedPlayerPropsConfig(
                league="WNBA",
                prop_type=model_config['prop_type'],
                
                # Model architecture
                hidden_dim=model_config['hidden_dim'],
                num_layers=model_config['num_layers'],
                dropout_rate=0.4,
                use_batch_norm=False,  # Disable to avoid batch size issues
                
                # Training parameters
                num_epochs=model_config['epochs'],
                learning_rate=0.001,
                batch_size=64,  # Larger batch size to avoid BatchNorm issues
                early_stopping_patience=15,
                
                # Enhanced features
                add_interaction_features=True,
                feature_selection_threshold=0.03,
                max_features=50,
                
                # Loss function (MSE worked best for regression-to-mean fix)
                loss_function="mse",
                
                # Regularization
                weight_decay=0.01,
                use_calibration_layer=True,
                
                # Model save (simplified filename to avoid save issues)
                model_save_path=f"models/{model_config['prop_type']}_prod.pt"
            )
            
            # Initialize pipeline
            pipeline = EnhancedPlayerPropsTrainingPipeline(config)
            
            # Train model
            result = await pipeline.train()
            
            results[model_config['prop_type']] = {
                'success': True,
                'test_mae': result.get('test_mae', 'N/A'),
                'test_r2': result.get('test_r2', 'N/A'),
                'model_path': config.model_save_path
            }
            
            logger.info(f"✅ {model_config['prop_type'].upper()} model completed!")
            logger.info(f"   Test MAE: {results[model_config['prop_type']]['test_mae']}")
            logger.info(f"   Test R²: {results[model_config['prop_type']]['test_r2']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to train {model_config['prop_type']} model: {e}")
            results[model_config['prop_type']] = {
                'success': False,
                'error': str(e)
            }
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("🎯 PRODUCTION TRAINING SUMMARY")
    logger.info("=" * 60)
    
    successful_models = 0
    for prop_type, result in results.items():
        if result['success']:
            successful_models += 1
            logger.info(f"✅ {prop_type.upper()}: MAE={result['test_mae']:.4f}, R²={result['test_r2']:.4f}")
        else:
            logger.info(f"❌ {prop_type.upper()}: {result['error']}")
    
    logger.info(f"\n🎯 Successfully trained {successful_models}/{len(models_to_train)} models")
    
    return results

if __name__ == "__main__":
    asyncio.run(train_production_models())
