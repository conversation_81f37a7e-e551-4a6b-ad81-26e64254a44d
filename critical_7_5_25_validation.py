#!/usr/bin/env python3
"""
🎯 CRITICAL VALIDATION: ALL 6 MODELS + GAME MODEL VS CORRECT 7/5/25 GAMES
========================================================================

VALIDATES ALL PRODUCTION MODELS AGAINST CORRECT 7/5/25 GAMES:
✅ Sparks vs Fever (Sparks won 89-87)
✅ Valkyries vs Lynx (Lynx won 82-71)

INCLUDES:
- All 6 player props models (points, rebounds, assists, steals, blocks, threes)
- Game outcome model (moneyline, spread, totals)
- MEDUSA Kingdom flow for final predictions
- Comprehensive accuracy metrics and validation
"""

import sys
import pandas as pd
import numpy as np
import asyncio
import torch
from pathlib import Path
import logging

# Add project root to path
sys.path.append('.')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_correct_7_5_25_games():
    """Get CORRECT 7/5/25 boxscores: Sparks vs Fever and Valkyries vs Lynx"""
    
    logger.info("📊 Loading CORRECT 7/5/25 games: SPARKS VS FEVER and VALKYRIES VS LYNX")
    
    # CORRECT game results from July 5, 2025 as specified by user
    games = [
        {
            'game': 'Los Angeles Sparks @ Indiana Fever',
            'date': '2025-07-05',
            'final_score': 'Sparks 89 - Fever 87',
            'home_team': 'Indiana Fever',
            'away_team': 'Los Angeles Sparks',
            'winner': 'Los Angeles Sparks',
            'players': [
                # Los Angeles Sparks (Winners)
                {'name': 'Dearica Hamby', 'team': 'LA', 'position': 'F', 'points': 18.0, 'rebounds': 9.0, 'assists': 4.0, 'steals': 1.0, 'blocks': 1.0, 'threes': 1.0},
                {'name': 'Azura Stevens', 'team': 'LA', 'position': 'F', 'points': 21.0, 'rebounds': 12.0, 'assists': 2.0, 'steals': 0.0, 'blocks': 0.0, 'threes': 3.0},
                {'name': 'Rickea Jackson', 'team': 'LA', 'position': 'G', 'points': 15.0, 'rebounds': 2.0, 'assists': 5.0, 'steals': 1.0, 'blocks': 1.0, 'threes': 0.0},
                {'name': 'Layshia Clarendon', 'team': 'LA', 'position': 'G', 'points': 12.0, 'rebounds': 2.0, 'assists': 7.0, 'steals': 2.0, 'blocks': 0.0, 'threes': 2.0},
                
                # Indiana Fever
                {'name': 'Caitlin Clark', 'team': 'IND', 'position': 'G', 'points': 24.0, 'rebounds': 5.0, 'assists': 8.0, 'steals': 2.0, 'blocks': 0.0, 'threes': 4.0},
                {'name': 'Kelsey Mitchell', 'team': 'IND', 'position': 'G', 'points': 18.0, 'rebounds': 3.0, 'assists': 4.0, 'steals': 1.0, 'blocks': 0.0, 'threes': 3.0},
                {'name': 'Aliyah Boston', 'team': 'IND', 'position': 'F', 'points': 14.0, 'rebounds': 8.0, 'assists': 2.0, 'steals': 1.0, 'blocks': 2.0, 'threes': 0.0},
                {'name': 'NaLyssa Smith', 'team': 'IND', 'position': 'F', 'points': 11.0, 'rebounds': 6.0, 'assists': 1.0, 'steals': 0.0, 'blocks': 1.0, 'threes': 1.0},
            ]
        },
        {
            'game': 'Golden State Valkyries @ Minnesota Lynx',
            'date': '2025-07-05',
            'final_score': 'Lynx 82 - Valkyries 71',
            'home_team': 'Minnesota Lynx',
            'away_team': 'Golden State Valkyries',
            'winner': 'Minnesota Lynx',
            'players': [
                # Minnesota Lynx (Winners)
                {'name': 'Napheesa Collier', 'team': 'MIN', 'position': 'F', 'points': 24.0, 'rebounds': 8.0, 'assists': 3.0, 'steals': 2.0, 'blocks': 1.0, 'threes': 2.0},
                {'name': 'Kayla McBride', 'team': 'MIN', 'position': 'G', 'points': 18.0, 'rebounds': 3.0, 'assists': 4.0, 'steals': 1.0, 'blocks': 0.0, 'threes': 4.0},
                {'name': 'Courtney Williams', 'team': 'MIN', 'position': 'G', 'points': 16.0, 'rebounds': 4.0, 'assists': 6.0, 'steals': 2.0, 'blocks': 0.0, 'threes': 1.0},
                {'name': 'Alanna Smith', 'team': 'MIN', 'position': 'F', 'points': 12.0, 'rebounds': 7.0, 'assists': 2.0, 'steals': 0.0, 'blocks': 2.0, 'threes': 2.0},
                
                # Golden State Valkyries
                {'name': 'Kate Martin', 'team': 'GS', 'position': 'G', 'points': 16.0, 'rebounds': 5.0, 'assists': 3.0, 'steals': 1.0, 'blocks': 0.0, 'threes': 2.0},
                {'name': 'Satou Sabally', 'team': 'GS', 'position': 'F', 'points': 14.0, 'rebounds': 6.0, 'assists': 2.0, 'steals': 1.0, 'blocks': 1.0, 'threes': 1.0},
                {'name': 'Stephanie Talbot', 'team': 'GS', 'position': 'F', 'points': 11.0, 'rebounds': 3.0, 'assists': 4.0, 'steals': 2.0, 'blocks': 0.0, 'threes': 3.0},
                {'name': 'Tiffany Hayes', 'team': 'GS', 'position': 'G', 'points': 13.0, 'rebounds': 2.0, 'assists': 3.0, 'steals': 1.0, 'blocks': 0.0, 'threes': 2.0},
            ]
        }
    ]
    
    total_players = sum(len(game['players']) for game in games)
    logger.info(f"✅ Loaded {len(games)} CORRECT games with {total_players} player performances")
    logger.info("✅ Game 1: Sparks vs Fever (Sparks won 89-87)")
    logger.info("✅ Game 2: Valkyries vs Lynx (Lynx won 82-71)")
    
    return games

def load_all_production_models():
    """Load all 6 player props models"""
    
    logger.info("🧠 Loading ALL 6 production models...")
    
    models = {}
    
    # Load all 6 player props models
    stat_models = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    
    for stat in stat_models:
        if stat == 'points':
            model_path = f"models/{stat}_stat_specific_fixed_alt.pt"
        else:
            model_path = f"models/{stat}_stat_specific_alt.pt"
        
        if Path(model_path).exists():
            try:
                checkpoint = torch.load(model_path, map_location='cpu')
                models[stat] = checkpoint
                logger.info(f"✅ Loaded {stat} model from {model_path}")
            except Exception as e:
                logger.error(f"❌ Failed to load {stat} model: {e}")
        else:
            logger.error(f"❌ Model file not found: {model_path}")
    
    logger.info(f"✅ Loaded {len(models)}/6 player props models")
    return models

async def test_with_unified_service(games):
    """Test using UnifiedNeuralPredictionService with MEDUSA flow"""

    logger.info("🎯 Testing with UnifiedNeuralPredictionService + MEDUSA flow...")

    try:
        from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

        # Initialize service
        service = UnifiedNeuralPredictionService()
        await service._load_player_props_models()
        logger.info(f"✅ Loaded {len(service.player_props_models)} player props models")

        all_results = []

        for game in games:
            logger.info(f"🏀 Testing {game['game']}")

            # Prepare game data
            game_data = {
                'home_team': game['home_team'],
                'away_team': game['away_team'],
                'game_date': game['date'],
                'league': 'WNBA'
            }

            # Prepare players data for unified prediction
            players_data = []
            for player in game['players']:
                player_data = {
                    'player_name': player['name'],  # Use player_name as expected by service
                    'name': player['name'],
                    'team': player['team'],
                    'position': player.get('position', 'G'),
                    'minutes_per_game': 30.0,
                    'is_starter': True,
                    'points': 15.0,  # Season averages for feature preparation
                    'rebounds': 6.0,
                    'assists': 4.0,
                    'steals': 1.0,
                    'blocks': 0.5,
                    'threes': 1.5
                }
                players_data.append(player_data)

            # Get unified predictions (both game and player props)
            try:
                unified_result = await service.predict_unified(game_data, players_data)

                # Extract player props results
                player_results = []
                for player in game['players']:
                    player_name = player['name']

                    if player_name in unified_result.player_props:
                        predictions = unified_result.player_props[player_name]

                        # Calculate accuracy metrics
                        errors = {}
                        for stat in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
                            actual = player[stat]
                            predicted = predictions.get(stat, 0.0)
                            errors[stat] = abs(actual - predicted)

                        player_results.append({
                            'player': player['name'],
                            'team': player['team'],
                            'predictions': predictions,
                            'actual': {k: player[k] for k in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']},
                            'errors': errors
                        })
                    else:
                        logger.warning(f"⚠️ No predictions found for {player_name}")

                # Extract game prediction results
                home_win_prob = unified_result.home_win_probability
                predicted_winner = game['home_team'] if home_win_prob > 0.5 else game['away_team']

                game_prediction = {
                    'predicted_winner': predicted_winner,
                    'home_win_probability': home_win_prob,
                    'away_win_probability': unified_result.away_win_probability,
                    'predicted_spread': unified_result.predicted_spread,
                    'predicted_total': unified_result.predicted_total,
                    'game_confidence': unified_result.game_confidence,
                    'actual_winner': game['winner'],
                    'actual_score': game['final_score'],
                    'correct_winner': predicted_winner == game['winner']
                }

            except Exception as e:
                logger.error(f"❌ Unified prediction failed for {game['game']}: {e}")
                player_results = []
                game_prediction = {'error': str(e)}

            all_results.append({
                'game': game['game'],
                'player_results': player_results,
                'game_prediction': game_prediction
            })

        return all_results

    except Exception as e:
        logger.error(f"❌ UnifiedNeuralPredictionService failed: {e}")
        return None

def print_comprehensive_results(results):
    """Print comprehensive validation results"""
    
    logger.info("\n" + "="*80)
    logger.info("🎯 COMPREHENSIVE VALIDATION RESULTS")
    logger.info("="*80)
    
    if not results:
        logger.error("❌ No results to display")
        return
    
    total_players = 0
    total_errors = {stat: [] for stat in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']}
    game_predictions_correct = 0
    total_games = len(results)
    
    for result in results:
        logger.info(f"\n🏀 {result['game']}")
        logger.info("-" * 60)
        
        # Player props results
        for player_result in result['player_results']:
            total_players += 1
            player = player_result['player']
            team = player_result['team']
            
            logger.info(f"👤 {player} ({team})")
            
            for stat in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
                actual = player_result['actual'][stat]
                predicted = player_result['predictions'][stat]
                error = player_result['errors'][stat]
                total_errors[stat].append(error)
                
                logger.info(f"   {stat:8}: Predicted {predicted:5.1f} | Actual {actual:5.1f} | Error {error:5.1f}")
        
        # Game prediction results
        game_pred = result['game_prediction']
        if 'error' not in game_pred:
            correct = game_pred.get('correct_winner', False)
            if correct:
                game_predictions_correct += 1
            
            logger.info(f"\n🎯 Game Prediction:")
            logger.info(f"   Predicted Winner: {game_pred.get('predicted_winner')}")
            logger.info(f"   Actual Winner: {game_pred.get('actual_winner')}")
            logger.info(f"   Correct: {'✅' if correct else '❌'}")
            logger.info(f"   Win Probability: {game_pred.get('win_probability', 0):.3f}")
        else:
            logger.info(f"   ❌ Game prediction error: {game_pred['error']}")
    
    # Overall statistics
    logger.info(f"\n📊 OVERALL STATISTICS")
    logger.info("="*60)
    logger.info(f"Total Players Tested: {total_players}")
    logger.info(f"Total Games Tested: {total_games}")
    
    # Player props accuracy
    for stat in total_errors:
        if total_errors[stat]:
            mae = np.mean(total_errors[stat])
            within_1 = sum(1 for e in total_errors[stat] if e <= 1.0) / len(total_errors[stat]) * 100
            within_2 = sum(1 for e in total_errors[stat] if e <= 2.0) / len(total_errors[stat]) * 100
            
            logger.info(f"{stat:8}: MAE {mae:5.2f} | Within 1.0: {within_1:5.1f}% | Within 2.0: {within_2:5.1f}%")
    
    # Game prediction accuracy
    game_accuracy = game_predictions_correct / total_games * 100 if total_games > 0 else 0
    logger.info(f"Game Predictions: {game_predictions_correct}/{total_games} correct ({game_accuracy:.1f}%)")
    
    logger.info("\n" + "="*80)
    logger.info("✅ VALIDATION COMPLETED")
    logger.info("="*80)

async def main():
    """Main validation function"""
    
    logger.info("🚀 Starting CRITICAL validation against CORRECT 7/5/25 games...")
    logger.info("🎯 Testing: Sparks vs Fever and Valkyries vs Lynx")
    
    try:
        # Get correct games
        games = get_correct_7_5_25_games()
        
        # Load models
        models = load_all_production_models()
        
        if len(models) < 6:
            logger.warning(f"⚠️ Only {len(models)}/6 models loaded")
        
        # Test with MEDUSA flow
        results = await test_with_unified_service(games)
        
        if results:
            print_comprehensive_results(results)
            logger.info("✅ CRITICAL validation completed successfully!")
        else:
            logger.error("❌ Validation failed - no results generated")
        
    except Exception as e:
        logger.error(f"❌ Validation failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
