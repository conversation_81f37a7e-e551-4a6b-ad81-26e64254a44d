#!/usr/bin/env python3
"""
Test enhanced models using actual training data distribution
"""

import torch
import numpy as np
from enhanced_player_props_pipeline import EnhancedPlayerPropsModel, EnhancedPlayerPropsTrainingPipeline

def test_with_real_data():
    print("🔍 TESTING WITH REAL TRAINING DATA DISTRIBUTION")
    print("="*55)
    
    # Create training pipeline to get real data
    print("📊 Creating training data...")
    pipeline = EnhancedPlayerPropsTrainingPipeline()
    
    # Get training data for points
    X, y, feature_names = pipeline.create_enhanced_training_data('points')
    print(f"✅ Created training data: {X.shape[0]} samples, {X.shape[1]} features")
    print(f"📊 Target (points) stats: mean={y.mean():.2f}, std={y.std():.2f}, range=[{y.min():.1f}, {y.max():.1f}]")
    
    # Load the trained model
    print("\n📂 Loading trained points model...")
    checkpoint = torch.load('models/points_enhanced_model_FIXED_v2.pt', map_location='cpu')
    
    model = EnhancedPlayerPropsModel(
        input_dim=54,
        hidden_dims=[512, 256, 128, 64],
        dropout_rate=0.2,
        use_batch_norm=True
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Get scaler parameters
    scaler_params = checkpoint['feature_scaler_params']
    mean_array = np.array(scaler_params['mean_'])
    scale_array = np.array(scaler_params['scale_'])
    
    print("✅ Model loaded successfully")
    
    # Test with actual training samples
    print("\n🎯 Testing with real training samples:")
    
    # Take a few random samples from training data
    test_indices = np.random.choice(len(X), 5, replace=False)
    
    predictions = []
    actuals = []
    
    for i, idx in enumerate(test_indices):
        # Get real training sample
        real_features = X.iloc[idx].values
        real_target = y.iloc[idx]
        
        # Scale features using saved scaler
        scaled_features = (real_features - mean_array) / scale_array
        
        # Make prediction
        with torch.no_grad():
            features_tensor = torch.FloatTensor(scaled_features).unsqueeze(0)
            prediction = model(features_tensor).squeeze().item()
        
        predictions.append(prediction)
        actuals.append(real_target)
        
        print(f"   Sample {i+1}: Predicted={prediction:.1f}, Actual={real_target:.1f}, Error={abs(prediction-real_target):.1f}")
    
    # Calculate metrics
    mae = np.mean([abs(p - a) for p, a in zip(predictions, actuals)])
    print(f"\n📊 Test Results:")
    print(f"   MAE on real samples: {mae:.2f}")
    print(f"   Prediction range: [{min(predictions):.1f}, {max(predictions):.1f}]")
    print(f"   Actual range: [{min(actuals):.1f}, {max(actuals):.1f}]")
    
    # Check if predictions are realistic
    realistic_count = sum(1 for p in predictions if 2 <= p <= 35)
    print(f"   Realistic predictions: {realistic_count}/{len(predictions)}")
    
    if realistic_count == len(predictions) and mae < 15:
        print("✅ MODEL WORKING CORRECTLY with real data!")
        print("🔍 Issue is in synthetic feature generation, not the model")
        return True
    else:
        print("❌ Model still has issues even with real training data")
        return False

def test_simple_features():
    """Test with very simple, realistic features"""
    print("\n🔍 TESTING WITH SIMPLE REALISTIC FEATURES")
    print("="*45)
    
    # Load model
    checkpoint = torch.load('models/points_enhanced_model_FIXED_v2.pt', map_location='cpu')
    
    model = EnhancedPlayerPropsModel(
        input_dim=54,
        hidden_dims=[512, 256, 128, 64],
        dropout_rate=0.2,
        use_batch_norm=True
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Get scaler parameters
    scaler_params = checkpoint['feature_scaler_params']
    mean_array = np.array(scaler_params['mean_'])
    scale_array = np.array(scaler_params['scale_'])
    
    # Create simple test cases with realistic WNBA values
    test_cases = [
        ("Low scorer", [8, 3, 2, 0.5, 0.2, 1] + [0] * 48),  # 8 pts baseline
        ("Average player", [15, 5, 3, 1, 0.5, 2] + [0] * 48),  # 15 pts baseline  
        ("Star player", [22, 7, 5, 1.5, 1, 3] + [0] * 48)  # 22 pts baseline
    ]
    
    for name, features in test_cases:
        features_array = np.array(features[:54])  # Ensure 54 features
        
        # Scale features
        scaled_features = (features_array - mean_array) / scale_array
        
        # Make prediction
        with torch.no_grad():
            features_tensor = torch.FloatTensor(scaled_features).unsqueeze(0)
            prediction = model(features_tensor).squeeze().item()
        
        expected = features[0]  # First feature is points baseline
        error = abs(prediction - expected)
        
        print(f"🏀 {name}:")
        print(f"   Expected ~{expected} pts, Predicted: {prediction:.1f} pts, Error: {error:.1f}")
        
        if 2 <= prediction <= 35:
            print(f"   ✅ Realistic prediction")
        else:
            print(f"   ❌ Unrealistic prediction")

if __name__ == "__main__":
    # Test with real training data first
    real_data_works = test_with_real_data()
    
    # Test with simple features
    test_simple_features()
    
    if real_data_works:
        print("\n🎯 CONCLUSION:")
        print("✅ Model works correctly with real training data")
        print("🔧 Need to fix synthetic feature generation for validation")
        print("📋 Next: Create proper validation using actual WNBA player data")
    else:
        print("\n❌ CONCLUSION:")
        print("❌ Model has fundamental issues even with training data")
        print("🔧 Need to retrain model with better architecture/parameters")
